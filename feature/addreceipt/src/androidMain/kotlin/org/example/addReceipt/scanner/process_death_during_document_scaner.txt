System.out                AndroidDocumentScanner: Starting document scan
                          AndroidDocumentScanner: Getting start scan intent
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
System.out                AndroidDocumentScanner: Starting intent sender via launcher
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
libMEOW                   meow new tls: 0xb400007c7ef2b500
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007c7ef3a980
                          meow delete tls: 0xb400007c7ef2b500
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity@79f763, reason: AppDarkModeEnable
VRI[GmsDocu...teActivity] hardware acceleration = true, forceHwAccelerated = false
BufferQueueConsumer       [](id:649700000009,api:0,p:-1,c:25751) connect: controlledByApp=false
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#9](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2007840542034060(auto) mPendingTransactions.size=0 graphicBufferId=110599702839335 transform=0
Parcel                    Expecting binder but got null!
VRI[GmsDocu...teActivity] vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
DecorView[]               onWindowFocusChanged hasWindowFocus false
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#9](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[GmsDocumentScanningDelegateActivity]#9(BLAST Consumer)9](id:649700000009,api:0,p:-1,c:25751) disconnect
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
VRI[GmsDocu...teActivity] vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
BLASTBufferQueue          [VRI[MainActivity]#7](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[MainActivity]#7(BLAST Consumer)7](id:649700000007,api:0,p:-1,c:25751) disconnect
GED                       ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
                          ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS ENDED (25751) for package org.example.scanreceipt ----------------------------
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
Zygote                    process_name_ptr:26922 org.example.scanreceipt
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS STARTED (26922) for package org.example.scanreceipt ----------------------------
ziparchive                Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes2.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes5.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes19.dm': No such file or directory
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=0, found=7 (PCL[] | PCL[/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dex*3577277376:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes2.dex*1872147873:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes4.dex*829243340:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes5.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes8.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes19.dex*206416926])
nativeloader              Configuring clns-4 for other apk /data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/lib/arm64:/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/org.example.scanreceipt
                          Load libframework-connectivity-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity.jar: ok
GraphicsEnvironment       ANGLE Developer option for 'org.example.scanreceipt' set to: 'default'
                          ANGLE GameManagerService for org.example.scanreceipt: false
                          Neither updatable production driver nor prerelease driver is supported.
ForceDarkHelperStubImpl   initialize for org.example.scanreceipt , ForceDarkAppConfig: null
nativeloader              Load libforcedarkimpl.so using system ns (caller=/system_ext/framework/miui-framework.jar): ok
ple.scanreceipt           JNI_OnLoad success
MiuiForceDarkConfig       setConfig density:2.000000, mainRule:0, secondaryRule:0, tertiaryRule:0
NetworkSecurityConfig     No Network Security Config specified, using platform default
                          No Network Security Config specified, using platform default
System.err                SLF4J(W): No SLF4J providers were found.
                          SLF4J(W): Defaulting to no-operation (NOP) logger implementation
                          SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
MSYNC3-Vari...RefreshRate Variable refreshrate is disabled
nativeloader              Load libpowerhalwrap_jni.so using system ns (caller=/system/framework/mediatek-framework.jar): ok
PowerHalWrapper           PowerHalWrapper.getInstance
libMEOW                   meow new tls: 0xb400007cf6f8a980
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
libMEOW                   meow reload base cfg path: na
                          meow reload overlay cfg path: na
QT                        [QT]file does not exist
libMEOW                   applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007cf6fac5c0
                          meow delete tls: 0xb400007cf6f8a980
libc                      Access denied finding property "ro.vendor.df.effect.conflict"
ple.scanreceipt           type=1400 audit(0.0:1390060): avc: denied { read } for name="u:object_r:vendor_displayfeature_prop:s0" dev="tmpfs" ino=8399 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:vendor_displayfeature_prop:s0 tclass=file permissive=0 app=com.google.android.youtube
TransportRu...foScheduler Upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
                          Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
SurfaceFactory            [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@fe91512
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity@c73ac3f, reason: AppDarkModeEnable
VRI[GmsDocu...teActivity] hardware acceleration = true, forceHwAccelerated = false
libMEOW                   meow new tls: 0xb400007cf7023a00
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007cf70327c0
Looper                    PerfMonitor looperActivity : package=org.example.scanreceipt/com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity time=166ms latency=796ms  procState=-1  historyMsgCount=3
                          PerfMonitor looperActivity : package=org.example.scanreceipt/com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity time=0ms latency=960ms  procState=-1  historyMsgCount=4
FramePredict              FramePredict init: true
BufferQueueConsumer       [](id:692a00000000,api:0,p:-1,c:26922) connect: controlledByApp=false
libMEOW                   meow new tls: 0xb400007cf65226c0
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007cf6563e40
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
OpenGLRenderer            Device claims wide gamut support, cannot find matching config, error = EGL_SUCCESS
                          Failed to initialize 101010-2 format, error = EGL_SUCCESS
libc                      Access denied finding property "vendor.migl.debug"
libMiGL                   libmigl:This GPU version is note support Variable Shading Rate
libEGL                    pre_cache appList: ,,
FramePredict              "FramePredict won 't run in other process: org.example.scanreceipt"
ple.scanreceipt           MiuiProcessManagerServiceStub setSchedFifo
MiuiProcessManagerImpl    setSchedFifo pid:26922, mode:3
libc                      Access denied finding property "ro.vendor.magt.mtk_magt_support"
hw-ProcessState           Binder ioctl to enable oneway spam detection failed: Invalid argument
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ple.scanreceipt           DF open fail: No such file or directory
                          remote service is not exist
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
                          Access denied finding property "ro.vendor.display.iris_x7.support"
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#0](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2007850967898983(auto) mPendingTransactions.size=0 graphicBufferId=115629109542912 transform=0
Parcel                    Expecting binder but got null!
Looper                    PerfMonitor doFrame : time=135ms vsyncFrame=0 latency=317ms procState=-1 historyMsgCount=5
VRI[GmsDocu...teActivity] vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
                          vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
                          vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
DecorView[]               onWindowFocusChanged hasWindowFocus false
libMEOW                   meow new tls: 0xb400007c6d41ddc0
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007c6d42ec40
                          meow delete tls: 0xb400007c6d41ddc0
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
System.out                MainActivity: Activity result received with resultCode=-1
                          AndroidDocumentScanner: handleActivityResult called with resultCode=-1
                          AndroidDocumentScanner: No active scanner (weak reference cleared)
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for org.example.scanreceipt.MainActivity@6aca04f, reason: AppDarkModeEnable
VRI[MainActivity]         hardware acceleration = true, forceHwAccelerated = false
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
System.out                2025-08-03T06:55:30.475774Z
nativeloader              Load /data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!/lib/arm64-v8a/libsqliteJni.so using ns clns-4 from class loader (caller=/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!classes21.dex): ok
DynamiteModule            Local module descriptor class for com.google.mlkit.dynamite.text.latin not found.
DecoupledTextDelegate     Start loading thin OCR module.
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
DynamiteModule            Considering local module com.google.android.gms.vision.ocr:0 and remote module com.google.android.gms.vision.ocr:252635000
                          Selected remote version of com.google.android.gms.vision.ocr, version >= 252635000
                          Dynamite loader version >= 2, using loadModule2NoCrashUtils
OnBackInvokedCallback     OnBackInvokedCallback is not enabled for the application.
                          Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
System                    ClassLoader referenced unknown path:
nativeloader              Configuring clns-5 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/lib/arm64:/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
TransportRu...eEventStore Storing event with priority=DEFAULT, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
ple.scanreceipt           Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (unsupported, reflection, allowed)
Compatibili...ngeReporter Compat change id reported: 183155436; UID 10608; state: ENABLED
DynamiteModule            Considering local module com.google.android.gms.mlkit_ocr_common:0 and remote module com.google.android.gms.mlkit_ocr_common:252635000
                          Selected remote version of com.google.android.gms.mlkit_ocr_common, version >= 252635000
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
                          Failed to get app version for libraryName: optional-module-text-latin
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
ple.scanreceipt           Compiler allocated 5181KB to compile void android.view.ViewRootImpl.performTraversals()
View                      [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.AndroidComposeView{7d20428 VFED..... ......I. 0,0-0,0 aid=1073741829}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.ComposeView{31dec63 V.E...... ......ID 0,0-0,0}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.FrameLayout{9560060 V.E...... ......ID 0,0-0,0 #1020002 android:id/content}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.LinearLayout{7fb9919 V.E...... ......ID 0,0-0,0}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =DecorView@11bfdde[MainActivity]time =1395 ms
BufferQueueConsumer       [](id:692a00000001,api:0,p:-1,c:26922) connect: controlledByApp=false
VRI[MainActivity]         vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
BLASTBufferQueue          [VRI[MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=720x1600 mFrameNumber=1 applyTransaction=true mTimestamp=2007853456775830(auto) mPendingTransactions.size=0 graphicBufferId=115629109542919 transform=0
Parcel                    Expecting binder but got null!
Looper                    PerfMonitor longMsg : seq=32 plan=08:55:30.093 late=19ms wall=2285ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver procState=-1
                          PerfMonitor doFrame : time=2285ms vsyncFrame=0 latency=19ms procState=-1 historyMsgCount=5
VRI[MainActivity]         vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
OpenGLRenderer            Davey! duration=2303ms; Flags=1, FrameTimelineVsyncId=350124731, IntendedVsync=2007851154907291, Vsync=2007851171576231, InputEventId=0, HandleInputStart=2007851173471291, AnimationStart=2007851173477984, PerformTraversalsStart=2007851173483291, DrawStart=2007853338939907, FrameDeadline=2007851171907291, FrameInterval=2007851173244753, FrameStartTime=16668940, SyncQueued=2007853432784830, SyncStart=2007853432904522, IssueDrawCommandsStart=2007853433091830, SwapBuffers=2007853454852214, FrameCompleted=2007853458527599, DequeueBufferDuration=18538, QueueBufferDuration=554154, GpuCompleted=2007853458527599, SwapBuffersCompleted=2007853457488522, DisplayPresentTime=0, CommandSubmissionCompleted=2007853454852214,
Choreographer             Skipped 140 frames!  The application may be doing too much work on its main thread.
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
Looper                    PerfMonitor doFrame : time=47ms vsyncFrame=0 latency=2335ms procState=-1 historyMsgCount=6 (msgIndex=1 wall=2285ms seq=32 late=19ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
InsetsControllerImpl      change fadeInDuration from 500ms to 750ms for the lowmem and middle device
VRI[MainActivity]         vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
OpenGLRenderer            Davey! duration=2390ms; Flags=0, FrameTimelineVsyncId=350124797, IntendedVsync=2007851204257770, Vsync=2007853537980770, InputEventId=0, HandleInputStart=2007853539391599, AnimationStart=2007853539395522, PerformTraversalsStart=2007853566938368, DrawStart=2007853567467830, FrameDeadline=2007851237927220, FrameInterval=2007853539026599, FrameStartTime=16669450, SyncQueued=2007853586222907, SyncStart=2007853586316214, IssueDrawCommandsStart=2007853586586907, SwapBuffers=2007853588630291, FrameCompleted=2007853595028522, DequeueBufferDuration=20077, QueueBufferDuration=386692, GpuCompleted=2007853595028522, SwapBuffersCompleted=2007853590983522, DisplayPresentTime=0, CommandSubmissionCompleted=2007853588630291,
DecorView[]               onWindowFocusChanged hasWindowFocus true
HandWritingStubImpl       refreshLastKeyboardType: 1
                          getCurrentKeyboardType: 1
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#0](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[GmsDocumentScanningDelegateActivity]#0(BLAST Consumer)0](id:692a00000000,api:0,p:-1,c:26922) disconnect
VRI[MainActivity]         vri.Setup new sync id=1 syncSeqId=0

                          [          1754204132.836  1517: 1777 I/SmartPower.com.google.android.webview:sandboxed_process0:org.chromium.content.app.Sandbbackground->idle(4007ms) R(remove from whitelist) adj=0.
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
HandWritingStubImpl       getCurrentKeyboardType: 1
View                      [Warning] assignParent to null: this = DecorView@2e121bd[GmsDocumentScanningDelegateActivity]
ProfileInstaller          Installing profile for org.example.scanreceipt
ple.scanreceipt           Background concurrent copying GC freed 13MB AllocSpace bytes, 5(100KB) LOS objects, 49% free, 8136KB/15MB, paused 151us,40us total 126.426ms
System                    A resource failed to call close.
BLASTBufferQueue          [VRI[MainActivity]#1](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[MainActivity]#1(BLAST Consumer)1](id:692a00000001,api:0,p:-1,c:26922) disconnect
GED                       ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
DecorView[]               onWindowFocusChanged hasWindowFocus false
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
Looper                    PerfMonitor doFrame : time=505ms vsyncFrame=0 latency=30ms procState=-1 historyMsgCount=1
TransportRu...portBackend Making request to: https://firebaselogging.googleapis.com/v0cc/log/batch?format=json_proto3
                          Status Code: 200
                          Content-Type: application/json; charset=UTF-8
                          Content-Encoding: gzip
Looper                    PerfMonitor doFrame : time=420ms vsyncFrame=0 latency=8ms procState=-1
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }



Wiec w tym commicie mamy zajebiste skomplikowane recovery ktore nie dzialalo bo document scaner zapisywal zdjecie w cache i sie nie udawalo go odzyskac wiec trzeba bylo nie zapisywac w cache - gdybym umial czytac kod i wiedzial co kiedy sie dzieje tzn przesledzil co kiedy sie dzieje i to rozumial to mooooooze bym na to wpadl. Konwersacja:

Okej super! Pokazują się logi, mechanizm wydaje się działać dobrze, ale problem jest że
"AndroidImageLoader: File does not exist at path: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg"
Poniżej wklejam logi z system.out od czasu  wystartowania skanowania:
System.out                AndroidDocumentScanner: Starting document scan with ID: 1754208890555
                          AndroidDocumentScanner: Scan state saved - ID: 1754208890555
                          AndroidDocumentScanner: Saved scan state with ID: 1754208890555
                          AndroidDocumentScanner: Getting start scan intent
System.out                AndroidDocumentScanner: Starting intent sender via launcher
 PROCESS ENDED (23015) for package org.example.scanreceipt
 PROCESS STARTED (25860) for package org.example.scanreceipt
System.out                MainActivity: onCreate called
                          AndroidDocumentScanner: Launcher set
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY CHECK =====
                          AndroidDocumentScanner: Process ID: 25860
                          AndroidDocumentScanner: Current time: 1754208910910
                          AndroidDocumentScanner: Recovery check - Active: true, ScanID: 1754208890555, Timestamp: 1754208890563
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Found active scan, elapsed time: 20357ms (timeout: 300000ms)
                          AndroidDocumentScanner: Scan still valid, waiting for result...
                          AndroidDocumentScanner: ===== RECOVERY CHECK COMPLETE - WAITING =====
                          MainActivity: Found active scan after process restart, setting up recovery callback
                          AndroidDocumentScanner: Pending scan result callback set
System.out                MainActivity: Activity result received with resultCode=-1
                          AndroidDocumentScanner: ===== ACTIVITY RESULT RECEIVED =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Process ID: 25860
                          AndroidDocumentScanner: Current time: 1754208910954
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Pending callback: org.example.scanreceipt.MainActivity$$ExternalSyntheticLambda1@4234c5b
                          AndroidDocumentScanner: No active scanner (weak reference cleared) - checking for process death recovery
                          AndroidDocumentScanner: ===== HANDLING PROCESS DEATH RECOVERY =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Has data: true
                          AndroidDocumentScanner: Pending callback available: true
                          AndroidDocumentScanner: Processing result after process death
                          AndroidDocumentScanner: Scanning result: GmsDocumentScanningResult{pages=[Page{imageUri=file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg}], pdf=null}
                          AndroidDocumentScanner: Pages count: 1
                          AndroidDocumentScanner: Got 1 pages after process death
                          AndroidDocumentScanner: Image URI after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          AndroidDocumentScanner: Using pending callback for process death recovery
                          AndroidDocumentScanner: Calling callback with URI: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          MainActivity: Received scan result after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          MainActivity: Saving recovered scan result: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
System.out                MainActivity: Recovered scan result saved to SharedPreferences
                          AndroidDocumentScanner: Callback invoked successfully
                          AndroidDocumentScanner: Clearing pending callback
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY COMPLETE =====
System.out                ScanRecoveryAndroid: Checking for recovered scan result
                          ScanRecoveryAndroid: Recovered result: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg, timestamp: 1754208910961
                          ScanRecoveryAndroid: Found recovered scan result, elapsed: 2734ms
                          ScanRecoveryAndroid: Using file path directly: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          ScanRecoveryAndroid: Processing recovered scan: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          ScanRecoveryAndroid: Starting OCR processing for recovered scan
                          ScanRecoveryAndroid: OCR processing completed for recovered scan
Choreographer             Skipped 164 frames!  The application may be doing too much work on its main thread.
BitmapFactory             Unable to decode stream: java.io.FileNotFoundException: file:/data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg: open failed: ENOENT (No such file or directory)
System.out                AndroidImageLoader: File does not exist at path: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg

logi po naprawie (po skonczonej konwersacji ponizej):
ProfileInstaller          Installing profile for org.example.scanreceipt
MirrorManager             this model don't Support
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for org.example.scanreceipt.MainActivity@dd159b0, reason: AppDarkModeEnable
VRI[Wyskakujące okienko]  hardware acceleration = true, forceHwAccelerated = false
OnBackInvokedCallback     OnBackInvokedCallback is not enabled for the application.
                          Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
BufferQueueConsumer       [](id:701e00000001,api:0,p:-1,c:28702) connect: controlledByApp=false
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for org.example.scanreceipt.MainActivity@dd159b0, reason: AppDarkModeEnable
VRI[Wyskakujące okienko]  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
BLASTBufferQueue          [VRI[Wyskakujące okienko]#1](f:0,a:1) acquireNextBufferLocked size=385x288 mFrameNumber=1 applyTransaction=true mTimestamp=2012404533851024(auto) mPendingTransactions.size=0 graphicBufferId=123274151329799 transform=0
Parcel                    Expecting binder but got null!
VRI[Wyskakujące okienko]  vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
                          vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
DecorView[]               onWindowFocusChanged hasWindowFocus false
VRI[Wyskakujące okienko]  vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
HandWritingStubImpl       refreshLastKeyboardType: 1
                          getCurrentKeyboardType: 1
ple.scanreceipt           Background concurrent copying GC freed 14MB AllocSpace bytes, 5(164KB) LOS objects, 49% free, 5973KB/11MB, paused 233us,175us total 104.818ms
System                    A resource failed to call close.
OnBackInvokedCallback     OnBackInvokedCallback is not enabled for the application.
                          Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
System.out                2025-08-03T08:27:48.320865Z
DynamiteModule            Local module descriptor class for com.google.mlkit.dynamite.text.latin not found.
DecoupledTextDelegate     Start loading thin OCR module.
TransportRu...foScheduler Scheduling upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) with jobId=-663607673 in 55361ms(Backend next call timestamp 1754209723772). Attempt 1
Compatibili...ngeReporter Compat change id reported: 194532703; UID 10608; state: ENABLED
TransportRu...foScheduler Scheduling upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) with jobId=-658626936 in 86400000ms(Backend next call timestamp 0). Attempt 1
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
DynamiteModule            Considering local module com.google.android.gms.vision.ocr:0 and remote module com.google.android.gms.vision.ocr:252635000
                          Selected remote version of com.google.android.gms.vision.ocr, version >= 252635000
                          Dynamite loader version >= 2, using loadModule2NoCrashUtils
System                    ClassLoader referenced unknown path:
nativeloader              Configuring clns-5 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/lib/arm64:/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
TransportRu...eEventStore Storing event with priority=DEFAULT, name=FIREBASE_ML_SDK for destination cct
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
TransportRu...foScheduler Upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
ple.scanreceipt           Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (unsupported, reflection, allowed)
Compatibili...ngeReporter Compat change id reported: 183155436; UID 10608; state: ENABLED
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
DynamiteModule            Considering local module com.google.android.gms.mlkit_ocr_common:0 and remote module com.google.android.gms.mlkit_ocr_common:252635000
                          Selected remote version of com.google.android.gms.mlkit_ocr_common, version >= 252635000
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
View                      [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.AndroidComposeView{64b59f8 VFED..... ......ID 0,0-720,1600 aid=1073741824}time =1058 ms
                          [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.ComposeView{9f13cd9 V.E...... ......ID 0,0-720,1600}time =1058 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.FrameLayout{517369e V.E...... ......ID 0,0-720,1600 #1020002 android:id/content}time =1058 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.LinearLayout{9ba957f V.E...... ......ID 0,0-720,1600}time =1058 ms
                          [ANR Warning]onMeasure time too long, this =DecorView@d38894c[MainActivity]time =1059 ms
Looper                    PerfMonitor longMsg : seq=610 plan=10:27:48.233 late=47ms wall=1466ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver procState=-1
                          PerfMonitor doFrame : time=1466ms vsyncFrame=0 latency=47ms procState=-1 historyMsgCount=2
OpenGLRenderer            Davey! duration=1514ms; Flags=0, FrameTimelineVsyncId=350532308, IntendedVsync=2012407448030719, Vsync=2012407481364053, InputEventId=0, HandleInputStart=2012407495655563, AnimationStart=2012407495658332, PerformTraversalsStart=2012407667057717, DrawStart=2012408952504563, FrameDeadline=2012407481688391, FrameInterval=2012407495598101, FrameStartTime=16657672, SyncQueued=2012408956615871, SyncStart=2012408960649486, IssueDrawCommandsStart=2012408960763947, SwapBuffers=2012408962808255, FrameCompleted=2012408966694947, DequeueBufferDuration=40000, QueueBufferDuration=484846, GpuCompleted=2012408966694947, SwapBuffersCompleted=2012408964243024, DisplayPresentTime=0, CommandSubmissionCompleted=2012408962808255,
                          Davey! duration=1517ms; Flags=0, FrameTimelineVsyncId=350532308, IntendedVsync=2012407448030719, Vsync=2012407481364053, InputEventId=0, HandleInputStart=2012407495655563, AnimationStart=2012407495658332, PerformTraversalsStart=2012407667057717, DrawStart=2012408869129717, FrameDeadline=2012407465030719, FrameInterval=2012407495598101, FrameStartTime=16657672, SyncQueued=2012408950427640, SyncStart=2012408950854409, IssueDrawCommandsStart=2012408952165409, SwapBuffers=2012408956691255, FrameCompleted=2012408965589409, DequeueBufferDuration=45461, QueueBufferDuration=582692, GpuCompleted=2012408965589409, SwapBuffersCompleted=2012408959574717, DisplayPresentTime=0, CommandSubmissionCompleted=2012408956691255,
System.out                ScanRecoveryAndroid: Checking for recovered scan result
                          ScanRecoveryAndroid: Recovered result: null, timestamp: 0
                          ScanRecoveryAndroid: No recovered scan result found
Choreographer             Skipped 92 frames!  The application may be doing too much work on its main thread.
OpenGLRenderer            endAllActiveAnimators on 0xb400007c8abfeb00 (UnprojectedRipple) with handle 0xb400007c8a9cf220
View                      [Warning] assignParent to null: this = androidx.compose.ui.window.PopupLayout{733c3ce V.E...... ......ID 0,0-321,224 #1020002 android:id/content aid=1073741826}
BLASTBufferQueue          [VRI[Wyskakujące okienko]#1](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[Wyskakujące okienko]#1(BLAST Consumer)1](id:701e00000001,api:0,p:-1,c:28702) disconnect
Looper                    PerfMonitor doFrame : time=124ms vsyncFrame=0 latency=1536ms procState=-1 historyMsgCount=4 (msgIndex=1 wall=1466ms seq=610 late=47ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
OpenGLRenderer            Davey! duration=1692ms; Flags=0, FrameTimelineVsyncId=350532316, IntendedVsync=2012407498009769, Vsync=2012409031151497, InputEventId=0, HandleInputStart=2012409034396871, AnimationStart=2012409034400563, PerformTraversalsStart=2012409120442947, DrawStart=2012409153206947, FrameDeadline=2012407531663684, FrameInterval=2012409034189409, FrameStartTime=16653915, SyncQueued=2012409157615255, SyncStart=2012409157914101, IssueDrawCommandsStart=2012409158316794, SwapBuffers=2012409184498717, FrameCompleted=2012409191190101, DequeueBufferDuration=59616, QueueBufferDuration=501000, GpuCompleted=2012409191190101, SwapBuffersCompleted=2012409187352871, DisplayPresentTime=0, CommandSubmissionCompleted=2012409184498717,
DecorView[]               onWindowFocusChanged hasWindowFocus true
HandWritingStubImpl       refreshLastKeyboardType: 1
                          getCurrentKeyboardType: 1
System.out                AndroidDocumentScanner: Starting document scan with ID: 1754209674869
                          AndroidDocumentScanner: Scan state saved - ID: 1754209674869
                          AndroidDocumentScanner: Saved scan state with ID: 1754209674869
                          AndroidDocumentScanner: Getting start scan intent
Compatibili...ngeReporter Compat change id reported: 160794467; UID 10608; state: ENABLED
System.out                AndroidDocumentScanner: Starting intent sender via launcher
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
libMEOW                   meow new tls: 0xb400007c56531600
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007c56540c00
                          meow delete tls: 0xb400007c56531600
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity@abcb945, reason: AppDarkModeEnable
VRI[GmsDocu...teActivity] hardware acceleration = true, forceHwAccelerated = false
BufferQueueConsumer       [](id:701e00000002,api:0,p:-1,c:28702) connect: controlledByApp=false
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#2](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2012414232660871(auto) mPendingTransactions.size=0 graphicBufferId=123274151329802 transform=0
Parcel                    Expecting binder but got null!
VRI[GmsDocu...teActivity] vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
DecorView[]               onWindowFocusChanged hasWindowFocus false
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#2](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[GmsDocumentScanningDelegateActivity]#2(BLAST Consumer)2](id:701e00000002,api:0,p:-1,c:28702) disconnect
VRI[GmsDocu...teActivity] vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
BLASTBufferQueue          [VRI[MainActivity]#0](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[MainActivity]#0(BLAST Consumer)0](id:701e00000000,api:0,p:-1,c:28702) disconnect
GED                       ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS ENDED (28702) for package org.example.scanreceipt ----------------------------
Zygote                    process_name_ptr:29828 org.example.scanreceipt
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS STARTED (29828) for package org.example.scanreceipt ----------------------------
ziparchive                Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes2.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes5.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
                          Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes19.dm': No such file or directory
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=0, found=7 (PCL[] | PCL[/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dex*354686349:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes2.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes4.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes5.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes8.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes19.dex*766592785])
nativeloader              Configuring clns-4 for other apk /data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/lib/arm64:/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/org.example.scanreceipt
                          Load libframework-connectivity-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity.jar: ok
GraphicsEnvironment       ANGLE Developer option for 'org.example.scanreceipt' set to: 'default'
                          ANGLE GameManagerService for org.example.scanreceipt: false
                          Neither updatable production driver nor prerelease driver is supported.
ForceDarkHelperStubImpl   initialize for org.example.scanreceipt , ForceDarkAppConfig: null
nativeloader              Load libforcedarkimpl.so using system ns (caller=/system_ext/framework/miui-framework.jar): ok
ple.scanreceipt           JNI_OnLoad success
MiuiForceDarkConfig       setConfig density:2.000000, mainRule:0, secondaryRule:0, tertiaryRule:0
NetworkSecurityConfig     No Network Security Config specified, using platform default
                          No Network Security Config specified, using platform default
ple.scanreceipt           Verification of kotlin.Unit org.example.scanreceipt.MyApplication.$r8$lambda$gy6gLoxAva3X4sSopC8CwB8wCAE(org.example.scanreceipt.MyApplication, org.koin.core.KoinApplication) took 500.954ms (9.98 bytecodes/s) (2104B approximate peak alloc)
System.err                SLF4J(W): No SLF4J providers were found.
                          SLF4J(W): Defaulting to no-operation (NOP) logger implementation
                          SLF4J(W): See https://www.slf4j.org/codes.html#noProviders for further details.
Looper                    PerfMonitor longMsg : seq=3 plan=10:28:10.921 late=475ms wall=2481ms h=android.app.ActivityThread$H w=110 procState=-1
nativeloader              Load libpowerhalwrap_jni.so using system ns (caller=/system/framework/mediatek-framework.jar): ok
PowerHalWrapper           PowerHalWrapper.getInstance
MSYNC3-Vari...RefreshRate Variable refreshrate is disabled
libMEOW                   meow new tls: 0xb400007ce4894d40
                          meow reload base cfg path: na
                          meow reload overlay cfg path: na
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
QT                        [QT]file does not exist
libMEOW                   applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007ce48c71c0
                          meow delete tls: 0xb400007ce4894d40
TransportRu...foScheduler Upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
                          Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
libc                      Access denied finding property "ro.vendor.df.effect.conflict"
ple.scanreceipt           type=1400 audit(0.0:1393068): avc: denied { read } for name="u:object_r:vendor_displayfeature_prop:s0" dev="tmpfs" ino=8399 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:vendor_displayfeature_prop:s0 tclass=file permissive=0 app=org.example.scanreceipt
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
SurfaceFactory            [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@fe91512
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity@c73ac3f, reason: AppDarkModeEnable
VRI[GmsDocu...teActivity] hardware acceleration = true, forceHwAccelerated = false
libMEOW                   meow new tls: 0xb400007cf6f9aa00
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007cf6fa97c0
Looper                    PerfMonitor looperActivity : package=org.example.scanreceipt/com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity time=219ms latency=2986ms  procState=-1  historyMsgCount=3 (msgIndex=3 wall=2481ms seq=3 late=475ms h=android.app.ActivityThread$H w=110)
                          PerfMonitor looperActivity : package=org.example.scanreceipt/com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity time=0ms latency=3205ms  procState=-1  historyMsgCount=4 (msgIndex=3 wall=2481ms seq=3 late=475ms h=android.app.ActivityThread$H w=110)
FramePredict              FramePredict init: true
BufferQueueConsumer       [](id:748400000000,api:0,p:-1,c:29828) connect: controlledByApp=false
libMEOW                   meow new tls: 0xb400007cf64bd3c0
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007cf6502100
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
OpenGLRenderer            Device claims wide gamut support, cannot find matching config, error = EGL_SUCCESS
                          Failed to initialize 101010-2 format, error = EGL_SUCCESS
libc                      Access denied finding property "vendor.migl.debug"
libMiGL                   libmigl:This GPU version is note support Variable Shading Rate
libEGL                    pre_cache appList: ,,
FramePredict              "FramePredict won 't run in other process: org.example.scanreceipt"
ple.scanreceipt           MiuiProcessManagerServiceStub setSchedFifo
MiuiProcessManagerImpl    setSchedFifo pid:29828, mode:3
libc                      Access denied finding property "ro.vendor.magt.mtk_magt_support"
hw-ProcessState           Binder ioctl to enable oneway spam detection failed: Invalid argument
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ple.scanreceipt           DF open fail: No such file or directory
                          remote service is not exist
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
                          Access denied finding property "ro.vendor.display.iris_x7.support"
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#0](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2012433466771257(auto) mPendingTransactions.size=0 graphicBufferId=128110284505090 transform=0
Parcel                    Expecting binder but got null!
Looper                    PerfMonitor doFrame : time=116ms vsyncFrame=0 latency=465ms procState=-1 historyMsgCount=5 (msgIndex=1 wall=2481ms seq=3 late=475ms h=android.app.ActivityThread$H w=110)
VRI[GmsDocu...teActivity] vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
                          vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
DecorView[]               onWindowFocusChanged hasWindowFocus false
VRI[GmsDocu...teActivity] vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
libMEOW                   meow new tls: 0xb400007c6f095400
                          applied 1 plugins for [org.example.scanreceipt]:
                            plugin 1: [libMEOW_gift.so]: 0xb400007ce463f140
                          rebuild call chain: 0xb400007c6f0ae0c0
                          meow delete tls: 0xb400007c6f095400
MiuiMultiWindowAdapter    MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
IS_CTS_MODE               false
MULTI_WINDOW_ENABLED      false
DecorView[]               getWindowModeFromSystem  windowmode is 1
System.out                MainActivity: onCreate called
                          AndroidDocumentScanner: Launcher set
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY CHECK =====
                          AndroidDocumentScanner: Process ID: 29828
                          AndroidDocumentScanner: Current time: 1754209694434
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
System.out                AndroidDocumentScanner: Recovery check - Active: true, ScanID: 1754209674869, Timestamp: 1754209674873
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Found active scan, elapsed time: 19572ms (timeout: 300000ms)
                          AndroidDocumentScanner: Scan still valid, waiting for result...
                          AndroidDocumentScanner: ===== RECOVERY CHECK COMPLETE - WAITING =====
                          MainActivity: Found active scan after process restart, setting up recovery callback
                          AndroidDocumentScanner: Pending scan result callback set
                          MainActivity: Activity result received with resultCode=-1
                          AndroidDocumentScanner: ===== ACTIVITY RESULT RECEIVED =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Process ID: 29828
                          AndroidDocumentScanner: Current time: 1754209694492
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Pending callback: org.example.scanreceipt.MainActivity$$ExternalSyntheticLambda1@4234c5b
                          AndroidDocumentScanner: No active scanner (weak reference cleared) - checking for process death recovery
                          AndroidDocumentScanner: ===== HANDLING PROCESS DEATH RECOVERY =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Has data: true
                          AndroidDocumentScanner: Pending callback available: true
                          AndroidDocumentScanner: Processing result after process death
                          AndroidDocumentScanner: Scanning result: GmsDocumentScanningResult{pages=[Page{imageUri=file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg}], pdf=null}
                          AndroidDocumentScanner: Pages count: 1
                          AndroidDocumentScanner: Got 1 pages after process death
                          AndroidDocumentScanner: Image URI after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg
                          AndroidDocumentScanner: Using pending callback for process death recovery
                          AndroidDocumentScanner: Calling callback with URI: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg
                          MainActivity: Received scan result after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg
                          MainActivity: Saving recovered scan result: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg
                          MainActivity: Copying file from URI: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786991569338994.jpg
                          MainActivity: Successfully copied file to: /data/user/0/org.example.scanreceipt/files/receipt_recovered_1754209694495.jpg, size: 477313 bytes
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
System.out                MainActivity: Recovered scan result saved to SharedPreferences: /data/user/0/org.example.scanreceipt/files/receipt_recovered_1754209694495.jpg
                          AndroidDocumentScanner: Callback invoked successfully
                          AndroidDocumentScanner: Clearing pending callback
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY COMPLETE =====
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for org.example.scanreceipt.MainActivity@3c05337, reason: AppDarkModeEnable
VRI[MainActivity]         hardware acceleration = true, forceHwAccelerated = false
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
System.out                2025-08-03T08:28:14.928959Z
nativeloader              Load /data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!/lib/arm64-v8a/libsqliteJni.so using ns clns-4 from class loader (caller=/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk!classes21.dex): ok
DynamiteModule            Local module descriptor class for com.google.mlkit.dynamite.text.latin not found.
DecoupledTextDelegate     Start loading thin OCR module.
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
DynamiteModule            Considering local module com.google.android.gms.vision.ocr:0 and remote module com.google.android.gms.vision.ocr:252635000
                          Selected remote version of com.google.android.gms.vision.ocr, version >= 252635000
                          Dynamite loader version >= 2, using loadModule2NoCrashUtils
System                    ClassLoader referenced unknown path:
nativeloader              Configuring clns-5 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/lib/arm64:/data/app/~~aBZ7jYzQrGJJDuw27IHujA==/com.google.android.gms-pnUQd-dOUV_SD81MKmwFcQ==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
OnBackInvokedCallback     OnBackInvokedCallback is not enabled for the application.
                          Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/0000051e/dl-VisionOcr.optional_252635100000.dm': No such file or directory
TransportRu...eEventStore Storing event with priority=DEFAULT, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, DEFAULT, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
ple.scanreceipt           Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (unsupported, reflection, allowed)
Compatibili...ngeReporter Compat change id reported: 183155436; UID 10608; state: ENABLED
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
DynamiteModule            Considering local module com.google.android.gms.mlkit_ocr_common:0 and remote module com.google.android.gms.mlkit_ocr_common:252635000
                          Selected remote version of com.google.android.gms.mlkit_ocr_common, version >= 252635000
ziparchive                Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
                          Unable to open '/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.dm': No such file or directory
ple.scanreceipt           Compiler allocated 5181KB to compile void android.view.ViewRootImpl.performTraversals()
LibraryVersion            Failed to get app version for libraryName: optional-module-text-latin
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
View                      [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.AndroidComposeView{a533b79 VFED..... ......I. 0,0-0,0 aid=1073741827}time =1323 ms
                          [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.ComposeView{7e5251 V.E...... ......ID 0,0-0,0}time =1323 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.FrameLayout{e83eab6 V.E...... ......ID 0,0-0,0 #1020002 android:id/content}time =1324 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.LinearLayout{e7293b7 V.E...... ......ID 0,0-0,0}time =1324 ms
                          [ANR Warning]onMeasure time too long, this =DecorView@654d724[MainActivity]time =1325 ms
BufferQueueConsumer       [](id:748400000001,api:0,p:-1,c:29828) connect: controlledByApp=false
VRI[MainActivity]         vri.reportNextDraw android.view.ViewRootImpl.performTraversals:3916 android.view.ViewRootImpl.doTraversal:2651 android.view.ViewRootImpl$TraversalRunnable.run:9819 android.view.Choreographer$CallbackRecord.run:1429 android.view.Choreographer$CallbackRecord.run:1437
                          vri.Setup new sync id=0 syncSeqId=0
BLASTBufferQueue          [VRI[MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=720x1600 mFrameNumber=1 applyTransaction=true mTimestamp=2012435976049103(auto) mPendingTransactions.size=0 graphicBufferId=128110284505095 transform=0
Parcel                    Expecting binder but got null!
Looper                    PerfMonitor longMsg : seq=32 plan=10:28:14.544 late=11ms wall=2208ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver procState=-1
                          PerfMonitor doFrame : time=2208ms vsyncFrame=0 latency=11ms procState=-1 historyMsgCount=4
VRI[MainActivity]         vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
OpenGLRenderer            Davey! duration=2219ms; Flags=1, FrameTimelineVsyncId=350540106, IntendedVsync=2012433759134919, Vsync=2012433759134919, InputEventId=0, HandleInputStart=2012433770711564, AnimationStart=2012433770734949, PerformTraversalsStart=2012433770749410, DrawStart=2012435873029718, FrameDeadline=2012433776134919, FrameInterval=2012433770227564, FrameStartTime=16662309, SyncQueued=2012435954403872, SyncStart=2012435954571026, IssueDrawCommandsStart=2012435954756795, SwapBuffers=2012435974122641, FrameCompleted=2012435978342026, DequeueBufferDuration=17923, QueueBufferDuration=614231, GpuCompleted=2012435978342026, SwapBuffersCompleted=2012435976832180, DisplayPresentTime=0, CommandSubmissionCompleted=2012435974122641,
System.out                ScanRecoveryAndroid: Checking for recovered scan result
                          ScanRecoveryAndroid: Recovered result: /data/user/0/org.example.scanreceipt/files/receipt_recovered_1754209694495.jpg, timestamp: 1754209694515
                          ScanRecoveryAndroid: Found recovered scan result, elapsed: 2263ms
                          ScanRecoveryAndroid: Using recovered file: /data/user/0/org.example.scanreceipt/files/receipt_recovered_1754209694495.jpg (size: 477313 bytes)
                          ScanRecoveryAndroid: Processing recovered scan: /data/user/0/org.example.scanreceipt/files/receipt_recovered_1754209694495.jpg
                          ScanRecoveryAndroid: Starting OCR processing for recovered scan
skia                      SkJpegCodec::onGetPixels +
ion                       ioctl c0044901 failed with code -1: Invalid argument
skia                      stream getLength() not supported, use temp buffer for loading stream, buffer addr 0x7c6de8c000 length 477313
                          LoadInputStreamToMem va 0x7c6de8c000  size 479616
DefaultDispatch           type=1400 audit(0.0:1393069): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Huffman Builder run in subthread
                          Tile Decoder (#thread:4, size:512 512 256 492x688, alignment:256x16)
DefaultDispatch           type=1400 audit(0.0:1393070): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
skia                      SkJpegCodec::onGetPixels -
nativeloader              Configuring clns-6 for other apk . target_sdk_version=35, uses_libraries=ALL, library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
                          Extending system_exposed_libraries: libapuwareapusys.mtk.so:libapuwareapusys_v2.mtk.so:libapuwarexrp.mtk.so:libapuwarexrp_v2.mtk.so:libapuwareutils.mtk.so:libapuwareutils_v2.mtk.so:libapuwarehmp.mtk.so:libmcv_runtime_usdk.mtk.so:libneuron_graph_delegate.mtk.so:libneuronusdk_adapter.mtk.so:libtflite_mtk.mtk.so:libarmnn_ndk.mtk.so:libcmdl_ndk.mtk.so:libnir_neon_driver_ndk.mtk.so:libmvpu_runtime.mtk.so:libmvpu_runtime_pub.mtk.so:libmvpu_engine_pub.mtk.so:libmvpu_pattern_pub.mtk.so:libmvpuop_mtk_cv.mtk.so:libmvpuop_mtk_nn.mtk.so:libteeservice_client.trustonic.so:libmisys_jni.xiaomi.so
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
Choreographer             Skipped 136 frames!  The application may be doing too much work on its main thread.
nativeloader              Load /data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.apk!/lib/arm64-v8a/libmlkit_google_ocr_pipeline_gms.so using isolated ns clns-6 (caller=/data/user_de/0/com.google.android.gms/app_chimera/m/00000518/dl-MlkitOcrCommon.optional_252635100400.apk): ok
DynamiteModule            Local module descriptor class for com.google.android.gms.tflite_dynamite not found.
                          Considering local module com.google.android.gms.tflite_dynamite:0 and remote module com.google.android.gms.tflite_dynamite:243930801
                          Selected remote version of com.google.android.gms.tflite_dynamite, version >= 243930801
skia                      SkJpegCodec::onGetPixels +
                          stream getLength() not supported, use temp buffer for loading stream, buffer addr 0x7c6de8c000 length 477313
                          LoadInputStreamToMem va 0x7c6de8c000  size 479616
ple.scanreceipt           type=1400 audit(0.0:1393071): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Huffman Builder run in subthread
ple.scanreceipt           type=1400 audit(0.0:1393072): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Tile Decoder (#thread:4, size:512 512 256 492x688, alignment:256x16)
DynamiteModule            Local module descriptor class for com.google.android.gms.googlecertificates not found.
                          Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
                          Selected remote version of com.google.android.gms.googlecertificates, version >= 7
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=1, found=8 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dex*354686349:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes2.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes4.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes5.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes8.dex***********:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes19.dex*766592785:/data/app/~~BKsMWs9EusWs87NWIchyVQ==/org.example.scanreceipt-Wj3LcXAvzTiyo7-XMgCo6w==/base.apk*644564319])
skia                      SkJpegCodec::onGetPixels -
nativeloader              Configuring clns-7 for other apk . target_sdk_version=35, uses_libraries=ALL, library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/00000350/dl-TfliteDynamiteDynamite.integ_243930801100400.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
                          Extending system_exposed_libraries: libapuwareapusys.mtk.so:libapuwareapusys_v2.mtk.so:libapuwarexrp.mtk.so:libapuwarexrp_v2.mtk.so:libapuwareutils.mtk.so:libapuwareutils_v2.mtk.so:libapuwarehmp.mtk.so:libmcv_runtime_usdk.mtk.so:libneuron_graph_delegate.mtk.so:libneuronusdk_adapter.mtk.so:libtflite_mtk.mtk.so:libarmnn_ndk.mtk.so:libcmdl_ndk.mtk.so:libnir_neon_driver_ndk.mtk.so:libmvpu_runtime.mtk.so:libmvpu_runtime_pub.mtk.so:libmvpu_engine_pub.mtk.so:libmvpu_pattern_pub.mtk.so:libmvpuop_mtk_cv.mtk.so:libmvpuop_mtk_nn.mtk.so:libteeservice_client.trustonic.so:libmisys_jni.xiaomi.so
                          Load /data/user_de/0/com.google.android.gms/app_chimera/m/00000350/dl-TfliteDynamiteDynamite.integ_243930801100400.apk!/lib/arm64-v8a/libtflite_gmscore_jni.so using isolated ns clns-7 (caller=/data/user_de/0/com.google.android.gms/app_chimera/m/00000350/dl-TfliteDynamiteDynamite.integ_243930801100400.apk): ok
TFLite-in-PlayServices    Initialized module.
native                    I0000 00:00:1754209697.186065   30062 asset_manager_util.cc:61] Created global reference to asset manager.
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
Looper                    PerfMonitor doFrame : time=341ms vsyncFrame=0 latency=2283ms procState=-1 historyMsgCount=8 (msgIndex=1 wall=2208ms seq=32 late=11ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
InsetsControllerImpl      change fadeInDuration from 500ms to 750ms for the lowmem and middle device
VRI[MainActivity]         vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
DecorView[]               onWindowFocusChanged hasWindowFocus true
HandWritingStubImpl       refreshLastKeyboardType: 1
                          getCurrentKeyboardType: 1
Manager                   DeviceManager::DeviceManager
ServerFlag                Failed to parse result of GetServerConfigurableFlag, errno=34
Manager                   findAvailableDevices
OpenGLRenderer            Davey! duration=2661ms; Flags=0, FrameTimelineVsyncId=350540194, IntendedVsync=2012433825811976, Vsync=2012436092478688, InputEventId=0, HandleInputStart=2012436108613795, AnimationStart=2012436108617103, PerformTraversalsStart=2012436340892180, DrawStart=2012436341490103, FrameDeadline=2012433859478643, FrameInterval=2012436108423026, FrameStartTime=16666667, SyncQueued=2012436448219641, SyncStart=2012436448390411, IssueDrawCommandsStart=2012436448902103, SwapBuffers=2012436480947872, FrameCompleted=2012436487668257, DequeueBufferDuration=44847, QueueBufferDuration=344231, GpuCompleted=2012436487668257, SwapBuffersCompleted=2012436483400180, DisplayPresentTime=0, CommandSubmissionCompleted=2012436480947872,
Manager                   Found interface mtk-gpu (version = ACCELERATOR_APUSYS)
                          Found interface mtk-neuron (version = ACCELERATOR_APUSYS)
libc                      Access denied finding property "ro.hardware.chipname"
native                    I0000 00:00:1754209697.315826   30078 resource_manager.cc:23] Number of optimal threads: 2
                          I0000 00:00:1754209697.316825   30079 text_classifier.cc:32] Creating classifier TfliteTextClassifier
                          I0000 00:00:1754209697.316869   30078 text_detector_thread_pool_context.cc:43] Compute manager max in flight region detector overwrite: 1
                          I0000 00:00:1754209697.317116   30079 common_util.h:41] Resizing Thread Pool: ocr_segm to 3
                          I0000 00:00:1754209697.317416   30078 common_util.h:41] Resizing Thread Pool: ocr_det_0 to 3
                          I0000 00:00:1754209697.322057   30079 tflite_lstm_client_base.cc:371] Resizing interpreter pool to 4
tflite                    Initialized TensorFlow Lite runtime.
native                    I0000 00:00:1754209697.324246   30078 tflite_detector_client_with_shape_cache.cc:77] Interpreter threads: 2
                          E0000 00:00:1754209697.324485   30078 tflite_wrapper.cc:784] INTERNAL: RET_CHECK failure (intelligence/mobile_acceleration/support_library/tflite_wrapper.cc:784) delegate_plugin_ Could not create XNNPack plugin. Have you linked in the XNNPack_plugin target?
                          Stack trace:
                          E0000 00:00:1754209697.324628   30078 tflite_detector_client_with_shape_cache.cc:194] Failure during initialization of interpreter.
                          I0000 00:00:1754209697.324659   30078 compute_resource_manager.cc:196] remove resource 0 from compute resource list
TFLite-in-PlayServices    Created interpreter.
native                    I0000 00:00:1754209697.326036   30078 tflite_detector_client_base.cc:353] Resizing interpreter pool to 4
TFLite-in-PlayServices    Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
libc                      Access denied finding property "ro.hardware.chipname"
TFLite-in-PlayServices    Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
native                    I0000 00:00:1754209697.347749   30078 multi_pass_line_recognition_mutator.cc:342] Preloading recognizers.
                          I0000 00:00:1754209697.347888   30078 init-domain.cc:126] Fiber init: default domain = pthread, concurrency = 8, prefix = pthread-default
                          I0000 00:00:1754209697.347995   30079 tflite_model_pooled_runner.cc:635] Loading mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite
                          I0000 00:00:1754209697.348484   30084 tflite_model_pooled_runner.cc:635] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb
                          I0000 00:00:1754209697.349838   30079 tflite_model_pooled_runner.cc:847] Resizing interpreter pool to 1
                          I0000 00:00:1754209697.351286   30084 tflite_model_pooled_runner.cc:646] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb
TFLite-in-PlayServices    Created interpreter.
native                    I0000 00:00:1754209697.353980   30079 tflite_model_pooled_runner.cc:635] Loading mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite
                          I0000 00:00:1754209697.356131   30084 tflite_model_pooled_runner.cc:847] Resizing interpreter pool to 4
                          I0000 00:00:1754209697.356697   30079 tflite_model_pooled_runner.cc:847] Resizing interpreter pool to 1
TFLite-in-PlayServices    Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
                          Created interpreter.
native                    I0000 00:00:1754209697.381798   30084 mobile_langid_v2.cc:58] MobileLangID V2 initialized.
                          I0000 00:00:1754209697.381904   30084 multi_pass_line_recognition_mutator.cc:364] Finished preloading a recognizer for "Latn"
                          I0000 00:00:1754209697.381983   30078 multi_pass_line_recognition_mutator.cc:398] Finished preloading recognizers.
                          I0000 00:00:1754209697.385525   30062 scheduler.cc:692] ImageMetadata: 1772x2708
VRI[MainActivity]         vri.Setup new sync id=1 syncSeqId=0
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#0](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[GmsDocumentScanningDelegateActivity]#0(BLAST Consumer)0](id:748400000000,api:0,p:-1,c:29828) disconnect
Looper                    PerfMonitor doFrame : time=235ms vsyncFrame=0 latency=383ms procState=-1 historyMsgCount=20
VRI[MainActivity]         vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
View                      [Warning] assignParent to null: this = DecorView@f3e54ff[GmsDocumentScanningDelegateActivity]
HandWritingStubImpl       getCurrentKeyboardType: 1
libc                      Access denied finding property "ro.vendor.display.iris_x7.support"
TFLite-in-PlayServices    Created interpreter.
Looper                    PerfMonitor doFrame : time=127ms vsyncFrame=0 latency=364ms procState=-1 historyMsgCount=6
TfLite                    Usage reporting not opted in -- dropping log event 1 status code 1
                          Usage reporting not opted in -- dropping log event 2 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
System.out                >>> Found reference text: 'PARAGON'
                          >>> Line angle from ML Kit: -3.4148393
                          >>> Skew detected: -3.4148393. Attempting rotation.
skia                      SkJpegCodec::onGetPixels +
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
skia                      stream getLength() not supported, use temp buffer for loading stream, buffer addr 0x7c6de5e000 length 477313
                          LoadInputStreamToMem va 0x7c6de5e000  size 479616
ple.scanreceipt           type=1400 audit(0.0:1393073): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Huffman Builder run in subthread
ple.scanreceipt           type=1400 audit(0.0:1393074): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Tile Decoder (#thread:4, size:512 512 256 492x688, alignment:256x16)
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
skia                      SkJpegCodec::onGetPixels -
System                    A resource failed to call close.
System.out                !!!!!!!!!!!!image rotated to /data/user/0/org.example.scanreceipt/cache/rotated_1754209698593.jpg
                          >>> Image rotated successfully to: /data/user/0/org.example.scanreceipt/cache/rotated_1754209698593.jpg
skia                      SkJpegCodec::onGetPixels +
                          stream getLength() not supported, use temp buffer for loading stream, buffer addr 0x7c2af98000 length 1963857
                          LoadInputStreamToMem va 0x7c2af98000  size 1966080
ple.scanreceipt           type=1400 audit(0.0:1393076): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Huffman Builder run in subthread
ple.scanreceipt           type=1400 audit(0.0:1393077): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Tile Decoder (#thread:4, size:512 512 512 394x704, alignment:256x16)
skia                      SkJpegCodec::onGetPixels -
Looper                    PerfMonitor doFrame : time=8ms vsyncFrame=0 latency=359ms procState=-1 historyMsgCount=1
TfLite                    Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
                          Usage reporting not opted in -- dropping log event 3 status code 1
System.out                >>> OCR on rotated image successful.
                          ScanRecoveryAndroid: OCR processing completed for recovered scan
skia                      SkJpegCodec::onGetPixels +
                          stream getLength() not supported, use temp buffer for loading stream, buffer addr 0x7c28d90000 length 1963857
                          LoadInputStreamToMem va 0x7c28d90000  size 1966080
libjpeg-alpha             Huffman Builder run in subthread
ple.scanreceipt           type=1400 audit(0.0:1393079): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
libjpeg-alpha             Tile Decoder (#thread:4, size:512 512 512 394x704, alignment:256x16)
ple.scanreceipt           type=1400 audit(0.0:1393080): avc: denied { search } for name="ppm" dev="proc" ino=4026531923 scontext=u:r:untrusted_app:s0:c96,c258,c512,c768 tcontext=u:object_r:proc_ppm:s0 tclass=dir permissive=0 app=org.example.scanreceipt
skia                      SkJpegCodec::onGetPixels -
ProfileInstaller          Installing profile for org.example.scanreceipt
Looper                    PerfMonitor doFrame : time=719ms vsyncFrame=0 latency=36ms procState=-1 historyMsgCount=2
OpenGLRenderer            Davey! duration=768ms; Flags=0, FrameTimelineVsyncId=350544220, IntendedVsync=2012444974042848, Vsync=2012445007373420, InputEventId=0, HandleInputStart=2012445010722027, AnimationStart=2012445010725103, PerformTraversalsStart=2012445274063565, DrawStart=2012445274195334, FrameDeadline=2012445007708134, FrameInterval=2012445010663565, FrameStartTime=16665286, SyncQueued=2012445728195873, SyncStart=2012445728605488, IssueDrawCommandsStart=2012445729131027, SwapBuffers=2012445736832257, FrameCompleted=2012445743253180, DequeueBufferDuration=68000, QueueBufferDuration=458385, GpuCompleted=2012445743253180, SwapBuffersCompleted=2012445739855950, DisplayPresentTime=2012443002231565, CommandSubmissionCompleted=2012445736832257,
Choreographer             Skipped 45 frames!  The application may be doing too much work on its main thread.
Looper                    PerfMonitor doFrame : time=155ms vsyncFrame=0 latency=757ms procState=-1 historyMsgCount=2 (msgIndex=1 wall=719ms seq=3203 late=36ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
OpenGLRenderer            Davey! duration=936ms; Flags=0, FrameTimelineVsyncId=350544228, IntendedVsync=2012445023957975, Vsync=2012445773807150, InputEventId=0, HandleInputStart=2012445780484257, AnimationStart=2012445780487950, PerformTraversalsStart=2012445891834950, DrawStart=2012445892054104, FrameDeadline=2012445057621290, FrameInterval=2012445780275257, FrameStartTime=16663315, SyncQueued=2012445935068488, SyncStart=2012445935286027, IssueDrawCommandsStart=2012445935485488, SwapBuffers=2012445954264257, FrameCompleted=2012445960754334, DequeueBufferDuration=93461, QueueBufferDuration=469769, GpuCompleted=2012445960754334, SwapBuffersCompleted=2012445956739565, DisplayPresentTime=2012443019055873, CommandSubmissionCompleted=2012445954264257,
TransportRu...portBackend Making request to: https://firebaselogging.googleapis.com/v0cc/log/batch?format=json_proto3
                          Status Code: 200
                          Content-Type: application/json; charset=UTF-8
                          Content-Encoding: gzip
DecorView[]               onWindowFocusChanged hasWindowFocus false
ActivityManagerWrapper    getRecentTasks: mainTaskId=18666   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
BLASTBufferQueue          [VRI[MainActivity]#1](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[MainActivity]#1(BLAST Consumer)1](id:748400000001,api:0,p:-1,c:29828) disconnect
GED                       ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0


_________cala konwersacja z augment:
Threads

Przeanalizuj logi w pliku .txt - czasami aplikacja jest ubijana podczas skanowania i scan result jest null. Wydaje mi sie ze jest to spowodowane niskim poziomem pamieci? Przemysl strategie i rozwiązanie tego problemu - oczekuje takiego zachowania aplikacji, że nawet po ubiciu procesu aplikacja będzie w stanie dostać od ml kit document scanner result - jak tego dokonać? Ktoś powiedział, że "Problem polega na tym, że gdy system zabija proces aplikacji, wszystkie statyczne zmienne (w tym activeScanner i activeContinuation) są resetowane. Gdy aplikacja się restartuje, nie ma już aktywnej coroutyny oczekującej na wynik." - dodaj również println aby dobrze zdebugować problem
09:02 AM
Przeanalizuj logi w pliku .txt -  czasami aplikacja jest ubijana podczas skanowania i scan result jest null. Wydaje mi sie ze jest to spowodowane niskim poziomem pamieci? Przemysl strategie i rozwiązanie tego problemu - oczekuje takiego zachowania aplikacji, że nawet po ubiciu procesu aplikacja będzie w stanie dostać od ml kit document scanner result - jak tego dokonać? Ktoś powiedział, że "Problem polega na tym, że gdy system zabija proces aplikacji, wszystkie statyczne zmienne (w tym activeScanner i activeContinuation) są resetowane. Gdy aplikacja się restartuje, nie ma już aktywnej coroutyny oczekującej na wynik." - dodaj również println aby dobrze zdebugować problem

Przeanalizuję problem z ubijaniem aplikacji podczas skanowania i zaproponuję rozwiązanie. Najpierw sprawdzę strukturę projektu i znajdę odpowiednie pliki.
Read directory
.
Teraz poszukam pliku .txt z logami:
Read file
openaiErrors.txt
misc-ScanReceipt
Ten plik zawiera błędy OpenAI, ale nie widzę logów dotyczących ubijania aplikacji podczas skanowania. Sprawdzę inne pliki w katalogu misc-ScanReceipt:
Read directory
misc-ScanReceipt
Sprawdzę inne pliki, może w jednym z nich są logi dotyczące skanowania:
Read file
todo prompty.txt
misc-ScanReceipt
Teraz sprawdzę kod związany ze skanowaniem dokumentów. Poszukam implementacji ML Kit Document Scanner:
Augment Context Engine
ML Kit Document Scanner implementation, scan result handling, document scanning functionality, activeScanner, activeContinuation variables
Teraz sprawdzę MainActivity i jak obsługuje wyniki skanowania:
Read file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Teraz sprawdzę czy są jakieś logi w logcat lub inne pliki z logami. Sprawdzę też implementację AndroidDocumentScanner bardziej szczegółowo:
Read file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz sprawdzę czy są jakieś pliki z logami w katalogu build lub inne miejsca:
Terminal
$ find . -name "*.log" -o -name "*log*.txt" -o -name "logcat*" 2>/dev/null
09:04 AM
w katalogu addReceipt>scanner znajduje sie plik "proces death during document scaner.txt" i to w nim znajdują się logi

Sprawdzę plik z logami dotyczącymi śmierci procesu podczas skanowania dokumentów:
Read file
proces death during document scaner.txt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Sprawdzę katalog scanner:
Read directory
scanner
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz sprawdzę plik z logami:
Read file
proces death during document scaner.txt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Spróbuję z dokładną nazwą pliku:
Terminal
$ ls -la "feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/"
Read file
proces death during document scaner.txt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Terminal
$ cat "feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/proces death during document scaner.txt"
Terminal
$ cat "feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/proces death during document scaner.txt"
Terminal
$ find . -name "*proces*" -type f
Terminal
$ cat "./feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/proces death during document scaner.txt"
Terminal
$ ls -la "./feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/"
Terminal
$ hexdump -C "./feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/proces death during document scaner.txt" | head -5
Terminal
$ cd "./feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/" && ls -la
Terminal
$ cd "./feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner/" && cat "proces death during document scaner.txt"
09:17 AM
dobra nie odczytuj tego pliku, wklejam z niego najważniejsze informacje:
System.out                AndroidDocumentScanner: Starting document scan
                          AndroidDocumentScanner: Getting start scan intent
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRwczovL2ZpcmViYXNlbG9nZ2luZy5nb29nbGVhcGlzLmNvbS92MGNjL2xvZy9iYXRjaD9mb3JtYXQ9anNvbl9wcm90bzNc) is already scheduled. Returning...
System.out                AndroidDocumentScanner: Starting intent sender via launcher
ransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
TransportRu...foScheduler Upload for context TransportContext(cct, VERY_LOW, MSRodHRw(...)
ForceDarkHelperStubImpl   setViewRootImplForceDark: false for com.google.mlkit.vision.documentscanner.internal.GmsDocumentScanningDelegateActivity@79f763, reason: AppDarkModeEnable
VRI[GmsDocu...teActivity] hardware acceleration = true, forceHwAccelerated = false
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#9](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2007840542034060(auto) mPendingTransactions.size=0 graphicBufferId=110599702839335 transform=0
Parcel                    Expecting binder but got null!
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#9](f:0,a:1) acquireNextBufferLocked size=720x1532 mFrameNumber=1 applyTransaction=true mTimestamp=2007840542034060(auto) mPendingTransactions.size=0 graphicBufferId=110599702839335 transform=0
Parcel                    Expecting binder but got null!
VRI[GmsDocu...teActivity] vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
DecorView[]               onWindowFocusChanged hasWindowFocus false
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
VRI[GmsDocu...teActivity] vri.reportNextDraw android.view.ViewRootImpl.handleResized:2024 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6069 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6033 android.os.Handler.dispatchMessage:106
BLASTBufferQueue          [VRI[GmsDocumentScanningDelegateActivity]#9](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[GmsDocumentScanningDelegateActivity]#9(BLAST Consumer)9](id:649700000009,api:0,p:-1,c:25751) disconnect
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
VRI[GmsDocu...teActivity] vri.Setup new sync id=1 syncSeqId=0
                          vri.reportDrawFinished syncSeqId=0 android.view.ViewRootImpl.lambda$createSyncIfNeeded$4$android-view-ViewRootImpl:3984 android.view.ViewRootImpl$$ExternalSyntheticLambda1.run:6 android.os.Handler.handleCallback:942 android.os.Handler.dispatchMessage:99 android.os.Looper.loopOnce:211
BLASTBufferQueue          [VRI[MainActivity]#7](f:0,a:1) destructor()
BufferQueueConsumer       [VRI[MainActivity]#7(BLAST Consumer)7](id:649700000007,api:0,p:-1,c:25751) disconnect
GED                       ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
                          ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 31, oppidx_max 31, oppidx_min 0
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS ENDED (25751) for package org.example.scanreceipt ----------------------------
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
                          getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
Zygote                    process_name_ptr:26922 org.example.scanreceipt
ActivityManagerWrapper    getRecentTasks: mainTaskId=18659   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=268435456 cmp=ComponentInfo{org.example.scanreceipt/org.example.scanreceipt.MainActivity} }
---------------------------- PROCESS STARTED (26922) for package org.example.scanreceipt ----------------------------
ziparchive                Unable to open '/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
ple.scanreceipt           ClassLoaderContext classpath size mismatch. expected=0, found=7 (PCL[] | PCL[/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes11.dex*3577277376:/data/data/org.example.scanreceipt/code_cache/.overlay/base.apk/classes10.dex*1204(...)
System.out                MainActivity: Activity result received with resultCode=-1
                          AndroidDocumentScanner: handleActivityResult called with resultCode=-1
                          AndroidDocumentScanner: No active scanner (weak reference cleared)
View                      [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.AndroidComposeView{7d20428 VFED..... ......I. 0,0-0,0 aid=1073741829}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =androidx.compose.ui.platform.ComposeView{31dec63 V.E...... ......ID 0,0-0,0}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.FrameLayout{9560060 V.E...... ......ID 0,0-0,0 #1020002 android:id/content}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =android.widget.LinearLayout{7fb9919 V.E...... ......ID 0,0-0,0}time =1394 ms
                          [ANR Warning]onMeasure time too long, this =DecorView@11bfdde[MainActivity]time =1395 ms


Choreographer             Skipped 140 frames!  The application may be doing too much work on its main thread.
OpenGLRenderer            Davey! duration=2390ms;
ple.scanreceipt           Background concurrent copying GC freed 13MB AllocSpace bytes, 5(100KB) LOS objects, 49% free, 8136KB/15MB, paused 151us,40us total 126.426ms

Analizując logi, widzę dokładnie problem, który opisałeś. Oto co się dzieje:
Analiza problemu
Proces skanowania rozpoczyna się normalnie:
System ubija proces aplikacji podczas skanowania:
Aplikacja restartuje się z nowym PID:
Wynik skanowania wraca, ale nie ma już aktywnej coroutiny:
MainActivity: Activity result received with resultCode=-1
AndroidDocumentScanner: handleActivityResult called with resultCode=-1
AndroidDocumentScanner: No active scanner (weak reference cleared)
Problem polega na tym, że statyczne zmienne activeScanner i activeContinuation są resetowane po restarcie procesu.
Strategia rozwiązania
Zaproponuję rozwiązanie oparte na persystencji stanu skanowania z wykorzystaniem SharedPreferences i mechanizmu odzyskiwania stanu po restarcie aplikacji.
Plan implementacji
Dodać mechanizm persystencji stanu skanowania
Zmodyfikować AndroidDocumentScanner aby zapisywał stan
Dodać mechanizm odzyskiwania stanu w MainActivity
Dodać dodatkowe logi debugowania
Zaimplementować timeout dla operacji skanowania
Czy chcesz, żebym przystąpił do implementacji tego rozwiązania?
Remember
User has process death issue during ML Kit document scanning where activeScanner and activeContinuation static variables are reset when system kills the app process, causing scan results to be lost when app restarts.
Teraz przystąpię do implementacji rozwiązania:
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz zmodyfikuję MainActivity aby obsługiwała odzyskiwanie stanu po śmierci procesu:
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Teraz muszę stworzyć lepszy mechanizm do obsługi odzyskiwania stanu. Sprawdzę jak jest zorganizowany AddReceiptViewModel i czy mogę dodać tam obsługę odzyskiwania:
Augment Context Engine
AddReceiptViewModel initialization, dependency injection setup, how AddReceiptViewModel is created and managed, UiStateManager, ReceiptManager, OcrManager classes and their initialization
Teraz stworzę lepszy mechanizm odzyskiwania stanu. Dodajmy nowy event do AddReceiptEvent i obsługę w ViewModel:
Read file
AddReceiptEvent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptEvent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz muszę stworzyć singleton lub mechanizm, który będzie przechowywał referencję do aktualnego ViewModelu, żeby MainActivity mogła przekazać wynik skanowania. Stworzę DocumentScannerRecoveryManager:
Created file
DocumentScannerRecoveryManager.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz zaktualizuję MainActivity aby używała tego managera:
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Teraz muszę zmodyfikować AddReceiptContent aby rejestrowała callback dla odzyskiwania:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz muszę dodać funkcję setupProcessDeathRecovery jako expect/actual. Najpierw dodajmy ją do commonMain:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Sprawdzę pełną strukturę importów w pliku:
Read lines 1-80
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Dodajmy import i funkcję expect na końcu pliku:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz dodajmy funkcję expect na końcu pliku:
Read lines 580--1
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz stworzę implementację dla Android:
Created file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz stworzę implementację dla iOS (pustą):
Created file
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz dodajmy więcej logów debugowania do AndroidDocumentScanner:
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Dodajmy też więcej logów do handleActivityResult:
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Dodajmy też logi do handleProcessDeathRecovery:
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz sprawdźmy czy wszystko się kompiluje:
Terminal
$ ./gradlew :composeApp:assembleDebug
10:01 AM
ok nice but you forgot to add expected declaration for "Actual function 'setupProcessDeathRecovery' has no corresponding expected declaration" and there is no usages in project for this function setupProcessDeathRecovery

Masz rację! Zapomniałem dodać deklarację expect i funkcja nie jest używana. Naprawmy to:
Najpierw dodajmy deklarację expect w commonMain:
Read lines 590--1
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Deklaracja expect już jest. Problem jest w tym, że funkcja nie jest wywoływana. Sprawdźmy gdzie powinna być wywoływana:
Read lines 85-110
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Funkcja jest wywoływana w linii 92. Problem może być w tym, że nie ma implementacji dla wszystkich platform. Sprawdźmy czy istnieją wszystkie pliki:
Read directory
src
feature/addreceipt
Sprawdźmy czy plik iOS istnieje:
Read file
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Sprawdźmy plik Android:
Read file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Pliki istnieją. Sprawdźmy czy może brakuje jakiejś platformy. Sprawdźmy strukturę projektu:
Read file
build.gradle.kts
feature/addreceipt
Projekt ma platformy Android i iOS. Sprawdźmy czy może jest problem z kompilacją. Spróbujmy skompilować tylko moduł addreceipt:
Terminal
$ ./gradlew :feature:addreceipt:compileKotlinAndroid
Spróbujmy z konkretnym wariantem:
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Aha! Problem jest w tym, że deklaracja expect nie jest widoczna dla implementacji actual. Sprawdźmy czy deklaracja expect jest w odpowiednim miejscu. Problem może być w tym, że funkcja jest zdefiniowana w pliku AddReceiptContent.kt, ale implementacje są w osobnych plikach.
Przenieśmy deklarację expect do osobnego pliku:
Created file
ProcessDeathRecovery.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz usuńmy deklarację z AddReceiptContent.kt:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz przenieśmy implementacje do odpowiednich plików z tą samą nazwą:
Edited file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Lepiej będzie przenieść implementację do pliku o tej samej nazwie. Usuńmy stary plik i stwórzmy nowy:
Remove
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Created file
ProcessDeathRecovery.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz zrobimy to samo dla iOS:
Remove
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Created file
ProcessDeathRecovery.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz spróbujmy skompilować ponownie:
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Musimy dodać import w AddReceiptContent.kt. Sprawdźmy gdzie jest wywołanie:
Read lines 88-95
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Funkcja jest wywoływana, ale nie ma importu. Ponieważ funkcja jest teraz w osobnym pliku, nie potrzebujemy importu - powinna być dostępna automatycznie w tym samym pakiecie. Sprawdźmy czy może problem jest w czymś innym.
Spróbujmy usunąć wywołanie funkcji na razie i sprawdźmy czy reszta się kompiluje:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Świetnie! Teraz się kompiluje. Problem był z expect/actual. Teraz naprawmy wywołanie funkcji. Spróbujmy prostszego podejścia - zamiast używać expect/actual, użyjmy prostego mechanizmu z callbackiem w MainActivity.
Usuńmy pliki expect/actual i zrobimy to prościej:
Remove
ProcessDeathRecovery.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
ProcessDeathRecovery.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
ProcessDeathRecovery.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz usuńmy wywołanie z AddReceiptContent:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz zmodyfikujmy DocumentScannerRecoveryManager aby był prostszy i działał bezpośrednio z ViewModelem:
Edited file
DocumentScannerRecoveryManager.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz zmodyfikujmy MainActivity aby ustawiała callback bezpośrednio z ViewModelem. Ale najpierw musimy stworzyć sposób na dostęp do ViewModelu z MainActivity.
Zamiast tego, zrobimy to prościej - zmodyfikujmy MainActivity aby przekazywała wynik bezpośrednio przez Intent lub SharedPreferences.
Zmodyfikujmy MainActivity:
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Dodajmy funkcję saveRecoveredScanResult:
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Teraz dodajmy mechanizm w AddReceiptViewModel aby sprawdzał SharedPreferences przy starcie:
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Dodajmy funkcję checkForRecoveredScanResult jako expect/actual:
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Właściwie, zrobimy to jeszcze prościej. Zamiast expect/actual, dodajmy mechanizm w OcrManager, który będzie sprawdzał SharedPreferences na Androidzie. Ale najpierw musimy przekazać Context do ViewModelu.
Sprawdźmy jak jest zorganizowane DI:
Read lines 110-130
KoinModule.kt
di/src/commonMain/kotlin/org/example/di
Zrobimy to jeszcze prościej. Dodajmy funkcję w AddReceiptContent, która będzie sprawdzać SharedPreferences tylko na Androidzie. Użyjemy LocalContext:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz dodajmy funkcję expect na końcu pliku:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz stwórzmy implementację dla Android:
Created file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz stwórzmy implementację dla iOS:
Created file
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz usuńmy niepotrzebną funkcję z AddReceiptViewModel:
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptViewModel.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz spróbujmy skompilować:
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Mamy dwa problemy:
Brak deklaracji expect
Funkcja @Composable nie może być suspend
Naprawmy to:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz naprawmy implementację Android:
Edited file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz musimy użyć LaunchedEffect wewnątrz funkcji:
Edited file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Edited file
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz naprawmy implementację iOS:
Edited file
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz spróbujmy skompilować:
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Command
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
Output
Here are the results from executing the command.
        <return-code>
        1
        </return-code>
        <stdout>

[1A[1m<[0;1m-------------> 0% INITIALIZING [43ms][m[38D[1B[1AReusing configuration cache.[0K
[0K

[2A[1m<[0;1m-------------> 0% INITIALIZING [140ms][m[39D[1B[1m> Loading configuration cache state[m[35D[1B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [240ms][m[0K[37D[1B[1m> :feature:addreceipt:compileDebugKotlinAndroid[m[47D[1B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [339ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [443ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [538ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [642ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [741ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [841ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [938ms][m[37D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [1s][m[0K[34D[2B[2A[1m<[0;32;1m============[0;39;1m-> 98% EXECUTING [2s][m[34D[2B[2A[0K
[31;1m> Task :feature:addreceipt:compileDebugKotlinAndroid[0;39m[31m FAILED[39m
e: file:///Users/<USER>/dev/kotlin/ScanReceipt/feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/AddReceiptContentAndroid.kt:18:12 'actual fun checkForRecoveredScanResult(onEvent: (AddReceiptEvent) -> Unit, textRecognizer: TextRecognizerML): Unit' has no corresponding expected declaration

[31mFAILURE: [39m[31mBuild failed with an exception.[39m

* What went wrong:
Execution failed for task ':feature:addreceipt:compileDebugKotlinAndroid'.
[33m> [39mA failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   [33m> [39mCompilation error. See log for more details

* Try:
[33m> [39mRun with [1m--stacktrace[m option to get the stack trace.
[33m> [39mRun with [1m--info[m or [1m--debug[m option to get more log output.
[33m> [39mRun with [1m--scan[m to get full insights.
[33m> [39mGet more help at [1mhttps://help.gradle.org[m.

[31;1mBUILD FAILED[0;39m in 2s
43 actionable tasks: 1 executed, 42 up-to-date
Configuration cache entry reused.
[0K
[0K
[2A[1m<[0;1m-------------> 0% WAITING[m[26D[1B> IDLE[6D[1B[2A[2K[1B[2K[1A[0m[?12l[?25h
        </stdout>
        <stderr>

        </stderr>
Nadal problem z expect/actual. Sprawdźmy czy deklaracja expect jest w odpowiednim miejscu:
Read lines 590--1
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Deklaracja jest tam. Problem może być w tym, że funkcja jest w pliku AddReceiptContent.kt, ale implementacje są w osobnych plikach. Spróbujmy przenieść deklarację do osobnego pliku:
Created file
ScanRecovery.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Usuńmy deklarację z AddReceiptContent.kt:
Edited file
AddReceiptContent.kt
feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt
Teraz przenieśmy implementacje do plików o tej samej nazwie:
Remove
AddReceiptContentAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
AddReceiptContentIos.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Created file
ScanRecovery.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Created file
ScanRecovery.kt
feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt
Teraz spróbujmy skompilować:
Terminal
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
10:22 AM
Okej super! Pokazują się logi, mechanizm wydaje się działać dobrze, ale problem jest że
"AndroidImageLoader: File does not exist at path: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg"
Poniżej wklejam logi z system.out od czasu  wystartowania skanowania:
System.out                AndroidDocumentScanner: Starting document scan with ID: 1754208890555
                          AndroidDocumentScanner: Scan state saved - ID: 1754208890555
                          AndroidDocumentScanner: Saved scan state with ID: 1754208890555
                          AndroidDocumentScanner: Getting start scan intent
System.out                AndroidDocumentScanner: Starting intent sender via launcher
 PROCESS ENDED (23015) for package org.example.scanreceipt
 PROCESS STARTED (25860) for package org.example.scanreceipt
System.out                MainActivity: onCreate called
                          AndroidDocumentScanner: Launcher set
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY CHECK =====
                          AndroidDocumentScanner: Process ID: 25860
                          AndroidDocumentScanner: Current time: 1754208910910
                          AndroidDocumentScanner: Recovery check - Active: true, ScanID: 1754208890555, Timestamp: 1754208890563
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Found active scan, elapsed time: 20357ms (timeout: 300000ms)
                          AndroidDocumentScanner: Scan still valid, waiting for result...
                          AndroidDocumentScanner: ===== RECOVERY CHECK COMPLETE - WAITING =====
                          MainActivity: Found active scan after process restart, setting up recovery callback
                          AndroidDocumentScanner: Pending scan result callback set
System.out                MainActivity: Activity result received with resultCode=-1
                          AndroidDocumentScanner: ===== ACTIVITY RESULT RECEIVED =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Process ID: 25860
                          AndroidDocumentScanner: Current time: 1754208910954
                          AndroidDocumentScanner: Active scanner reference: null
                          AndroidDocumentScanner: Active continuation: null
                          AndroidDocumentScanner: Pending callback: org.example.scanreceipt.MainActivity$$ExternalSyntheticLambda1@4234c5b
                          AndroidDocumentScanner: No active scanner (weak reference cleared) - checking for process death recovery
                          AndroidDocumentScanner: ===== HANDLING PROCESS DEATH RECOVERY =====
                          AndroidDocumentScanner: Result code: -1
                          AndroidDocumentScanner: Has data: true
                          AndroidDocumentScanner: Pending callback available: true
                          AndroidDocumentScanner: Processing result after process death
                          AndroidDocumentScanner: Scanning result: GmsDocumentScanningResult{pages=[Page{imageUri=file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg}], pdf=null}
                          AndroidDocumentScanner: Pages count: 1
                          AndroidDocumentScanner: Got 1 pages after process death
                          AndroidDocumentScanner: Image URI after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          AndroidDocumentScanner: Using pending callback for process death recovery
                          AndroidDocumentScanner: Calling callback with URI: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          MainActivity: Received scan result after process death: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          MainActivity: Saving recovered scan result: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
TransportRu...eEventStore Storing event with priority=VERY_LOW, name=FIREBASE_ML_SDK for destination cct
System.out                MainActivity: Recovered scan result saved to SharedPreferences
                          AndroidDocumentScanner: Callback invoked successfully
                          AndroidDocumentScanner: Clearing pending callback
                          AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY COMPLETE =====
System.out                ScanRecoveryAndroid: Checking for recovered scan result
                          ScanRecoveryAndroid: Recovered result: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg, timestamp: 1754208910961
                          ScanRecoveryAndroid: Found recovered scan result, elapsed: 2734ms
                          ScanRecoveryAndroid: Using file path directly: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          ScanRecoveryAndroid: Processing recovered scan: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg
                          ScanRecoveryAndroid: Starting OCR processing for recovered scan
                          ScanRecoveryAndroid: OCR processing completed for recovered scan
Choreographer             Skipped 164 frames!  The application may be doing too much work on its main thread.
BitmapFactory             Unable to decode stream: java.io.FileNotFoundException: file:/data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg: open failed: ENOENT (No such file or directory)
System.out                AndroidImageLoader: File does not exist at path: file:///data/user/0/org.example.scanreceipt/cache/mlkit_docscan_ui_client/3786208096095255.jpg


Świetnie! Mechanizm odzyskiwania po śmierci procesu działa poprawnie! Problem jest w tym, że ML Kit zapisuje plik w cache, ale po restarcie procesu ten plik już nie istnieje (prawdopodobnie został usunięty przez system).
Rozwiązanie: musimy skopiować plik z cache do trwałego miejsca w momencie otrzymania wyniku skanowania, zamiast polegać na ścieżce z cache.
Zmodyfikujmy handleProcessDeathRecovery w AndroidDocumentScanner aby kopiowała plik od razu:
Edited file
DocumentScannerAndroid.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/scanner
Teraz zmodyfikujmy MainActivity.saveRecoveredScanResult aby kopiowała plik od razu:
Edited file
MainActivity.kt
composeApp/src/androidMain/kotlin/org/example/scanreceipt
Teraz zmodyfikujmy ScanRecoveryAndroid aby nie próbowała konwertować URI, bo już mamy ścieżkę do pliku:
Edited file
ScanRecovery.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz usuńmy niepotrzebne importy:
Edited file
ScanRecovery.kt
feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt
Teraz sprawdźmy czy się kompiluje:
Terminal
$ ./gradlew :composeApp:assembleDebug
Waiting for user input...
9 files changed
+445 -4
Drop files to attach as context
images (png, jpg, jpeg)



