@file:OptIn(ExperimentalMaterial3Api::class)

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.*
import org.jetbrains.compose.ui.tooling.preview.Preview

// Klasa reprezentująca pojedy<PERSON> (je<PERSON><PERSON> nie ma<PERSON> j<PERSON> j<PERSON>definiowan<PERSON>)
data class DayItem(
    val day: String,        // np. "pn", "wt"
    val date: Int,          // numer dnia w miesiącu
    val month: String,      // np. "Lipiec"
    val isToday: Boolean = false,
    val isMonthStart: Boolean = false
)

// Obiekt pomocniczy (Twój kod)
object DateUtilsCurrentWeek {

    private val dayNames = listOf("nd", "pn", "wt", "śr", "cz", "pt", "sb")
    private val monthNames = listOf(
        "Styczeń", "Luty", "Marzec", "Kwiecień", "Maj", "Czerwiec",
        "Lipiec", "Sierpień", "Wrzesień", "Październik", "Listopad", "Grudzień"
    )

    /**
     * Get current week number (ISO week)
     */
    fun getCurrentWeekNumber(): Int {
        val now = Clock.System.now()
        val localDate = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        return getWeekNumberCurrentWeek(localDate)
    }

    /**
     * Get week number for specific date (ISO week)
     */
    private fun getWeekNumberCurrentWeek(date: LocalDate): Int {
        // ISO week calculation
        val jan4 = LocalDate(date.year, 1, 4)
        val jan4DayOfWeek = jan4.dayOfWeek.ordinal // Monday = 0
        val weekOneStart = jan4.minus(jan4DayOfWeek, DateTimeUnit.DAY)

        val daysDiff = date.daysUntil(weekOneStart)
        return (-daysDiff / 7) + 1
    }

    /**
     * Get current week days starting from Monday
     */
    fun getCurrentWeekDays(): List<DayItem> {
        val now = Clock.System.now()
        val today = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        val todayDayOfWeek = today.dayOfWeek.ordinal // Monday = 0

        // Get Monday of current week
        val monday = today.minus(todayDayOfWeek, DateTimeUnit.DAY)

        return (0..6).map { dayOffset ->
            val currentDate = monday.plus(dayOffset, DateTimeUnit.DAY)
            val isMonthStart = dayOffset == 0 ||
                    currentDate.month != monday.plus(dayOffset - 1, DateTimeUnit.DAY).month

            DayItem(
                day = dayNames[currentDate.dayOfWeek.ordinal],
                date = currentDate.dayOfMonth,
                month = monthNames[currentDate.monthNumber - 1],
                isToday = currentDate == today,
                isMonthStart = isMonthStart
            )
        }
    }

    /**
     * Get week days for specific week number and year
     */
    fun getWeekDays(weekNumber: Int, year: Int = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).year): List<DayItem> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date

        // Calculate first day of the specified week
        val jan4 = LocalDate(year, 1, 4)
        val jan4DayOfWeek = jan4.dayOfWeek.ordinal
        val weekOneStart = jan4.minus(jan4DayOfWeek, DateTimeUnit.DAY)
        val targetWeekStart = weekOneStart.plus((weekNumber - 1) * 7, DateTimeUnit.DAY)

        return (0..6).map { dayOffset ->
            val currentDate = targetWeekStart.plus(dayOffset, DateTimeUnit.DAY)
            val isMonthStart = dayOffset == 0 ||
                    currentDate.month != targetWeekStart.plus(dayOffset - 1, DateTimeUnit.DAY).month

            DayItem(
                day = dayNames[currentDate.dayOfWeek.ordinal],
                date = currentDate.dayOfMonth,
                month = monthNames[currentDate.monthNumber - 1],
                isToday = currentDate == today,
                isMonthStart = isMonthStart
            )
        }
    }
}

enum class CalendarVariant {
    AUTO,           // Automatycznie wykrywa czy tydzień zawiera jeden czy dwa miesiące
    SINGLE_MONTH,   // Wymusza wyświetlanie jako jeden miesiąc
    MULTI_MONTH     // Wymusza wyświetlanie jako wiele miesięcy
}

@Composable
fun CurrentWeekCalendarComponent3() {
    // Pobieranie rzeczywistych danych z DateUtils
    val currentWeekDays by remember { mutableStateOf(DateUtilsCurrentWeek.getCurrentWeekDays()) }
    val currentWeekNumber by remember { mutableStateOf(DateUtilsCurrentWeek.getCurrentWeekNumber()) }

    // Przykład poprzedniego tygodnia dla demonstracji
    val previousWeekDays by remember {
        mutableStateOf(DateUtilsCurrentWeek.getWeekDays(currentWeekNumber - 1))
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF9FAFB))
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Current Week Calendar Component",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Bieżący tydzień
        Text(
            text = "Bieżący tydzień",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF6B7280),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp, start = 8.dp),
            textAlign = TextAlign.Start
        )

        WeekCalendar(
            weekNumber = currentWeekNumber,
            days = currentWeekDays,
            variant = CalendarVariant.AUTO,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Poprzedni tydzień
        Text(
            text = "Poprzedni tydzień",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF6B7280),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp, start = 8.dp),
            textAlign = TextAlign.Start
        )

        WeekCalendar(
            weekNumber = currentWeekNumber - 1,
            days = previousWeekDays,
            variant = CalendarVariant.AUTO
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Design notes
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Usprawnienia designu:",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151),
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                val improvements = listOf(
                    "• Wykorzystuje kotlinx.datetime dla dokładnych dat",
                    "• Automatyczne wykrywanie aktualnego tygodnia",
                    "• Podkreślenie dzisiejszego dnia",
                    "• Inteligentne wyświetlanie miesięcy",
                    "• Responsywny design z animacjami",
                    "• Wsparcie dla ISO week numbers"
                )

                improvements.forEach { improvement ->
                    Text(
                        text = improvement,
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(vertical = 1.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun WeekCalendar(
    weekNumber: Int,
    days: List<DayItem>,
    variant: CalendarVariant = CalendarVariant.AUTO,
    modifier: Modifier = Modifier
) {
    val months = days.map { it.month }.distinct()
    val hasMultipleMonths = when (variant) {
        CalendarVariant.AUTO -> months.size > 1
        CalendarVariant.MULTI_MONTH -> true
        CalendarVariant.SINGLE_MONTH -> false
    }

    val currentYear = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).year

    Card(
        modifier = modifier
            .width(320.dp)
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(16.dp),
                ambientColor = Color.Black.copy(alpha = 0.1f)
            ),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Box(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.White,
                            Color(0xFFF9FAFB)
                        )
                    )
                )
                .padding(16.dp)
        ) {
            Column {
                // Header z numerem tygodnia
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    color = Color(0xFFFBBD23),
                                    shape = CircleShape
                                )
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "Tydzień $weekNumber",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF1F2937)
                        )
                    }

                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = Color(0xFFF3F4F6)
                    ) {
                        Text(
                            text = currentYear.toString(),
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280),
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }

                // Nagłówki miesięcy
                MonthHeaders(
                    days = days,
                    hasMultipleMonths = hasMultipleMonths,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                // Dni tygodnia
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    days.forEach { day ->
                        DayItemComposable(
                            dayItem = day,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }

                // Footer
                WeekFooter(
                    firstDay = days.first().date,
                    lastDay = days.last().date,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
        }
    }
}

@Composable
private fun MonthHeaders(
    days: List<DayItem>,
    hasMultipleMonths: Boolean,
    modifier: Modifier = Modifier
) {
    val months = days.map { it.month }.distinct()

    if (hasMultipleMonths && months.size == 2) {
        // Znajdź podział między miesiącami
        val monthChangeIndex = days.indexOfFirst { it.isMonthStart && it != days.first() }
        val firstMonthDays = if (monthChangeIndex > 0) monthChangeIndex else 4
        val secondMonthDays = 7 - firstMonthDays

        Row(
            modifier = modifier.fillMaxWidth()
        ) {
            // Pierwszy miesiąc
            Box(
                modifier = Modifier.weight(firstMonthDays.toFloat()),
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    shape = RoundedCornerShape(8.dp),
                    color = Color(0xFFEFF6FF)
                ) {
                    Text(
                        text = months[0],
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Drugi miesiąc
            Box(
                modifier = Modifier.weight(secondMonthDays.toFloat()),
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    shape = RoundedCornerShape(8.dp),
                    color = Color(0xFFF0FDF4)
                ) {
                    Text(
                        text = months[1],
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
        }
    } else {
        // Jeden miesiąc
        Row(
            modifier = modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center
        ) {
            Surface(
                shape = RoundedCornerShape(8.dp),
                color = Color(0xFFEFF6FF)
            ) {
                Text(
                    text = months[0],
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151),
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun WeekFooter(
    firstDay: Int,
    lastDay: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFF9FAFB),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(
                        color = Color(0xFFFBBD23),
                        shape = CircleShape
                    )
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = "Dziś",
                fontSize = 12.sp,
                color = Color(0xFF6B7280)
            )
        }

        Text(
            text = "$firstDay - $lastDay",
            fontSize = 12.sp,
            color = Color(0xFF9CA3AF)
        )
    }
}

@Composable
fun DayItemComposable(
    dayItem: DayItem,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (dayItem.isToday) 1.1f else if (isPressed) 0.95f else 1f,
        animationSpec = tween(200),
        label = "day_scale"
    )

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Numer dnia
        Box(
            modifier = Modifier
                .size(32.dp)
                .scale(scale)
                .clip(CircleShape)
                .background(
                    if (dayItem.isToday) {
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFFBBD23),
                                Color(0xFFF59E0B)
                            )
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFF3F4F6),
                                Color(0xFFF3F4F6)
                            )
                        )
                    }
                )
                .clickable {
                    isPressed = !isPressed
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = dayItem.date.toString(),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = if (dayItem.isToday) Color.White else Color(0xFF374151)
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        // Nazwa dnia
        Surface(
            shape = RoundedCornerShape(4.dp),
            color = if (dayItem.isToday) Color(0xFFFEF3C7) else Color.Transparent
        ) {
            Text(
                text = dayItem.day.uppercase(),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = if (dayItem.isToday) Color(0xFFD97706) else Color(0xFF6B7280),
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
            )
        }
    }
}

@Preview()
@Composable
fun CurrentWeekCalendarPreview() {
    MaterialTheme {
        CurrentWeekCalendarComponent3()
    }
}