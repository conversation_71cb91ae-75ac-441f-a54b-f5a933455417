package org.example.dashboard

/**/

import CurrentWeekCalendarComponent3
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import org.example.core.domain.model.Category
import org.example.dashboard.component.CurrentWeekCalendar2
import org.example.shared.component.DateFilterComponent
import org.example.shared.component.CurrentWeekCalendar
import org.koin.compose.viewmodel.koinViewModel
import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.sqrt

@Composable
fun DashboardScreen() {
    val viewModel = koinViewModel<DashboardViewModel>()
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
//                .padding(
//                    top = paddingValues.calculateTopPadding(),
//                    bottom = paddingValues.calculateBottomPadding()
//                )
                .padding(paddingValues)
        ) {
            DateFilterComponent(
                currentFilter = uiState.currentFilter,
                isExpanded = uiState.isFilterExpanded,
                availableFilters = uiState.availableFilters,
                onFilterSelected = viewModel::onFilterSelected,
                onExpandedChanged = viewModel::onFilterExpandedChanged,
//                modifier = Modifier
//                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )
//            Spacer(modifier = Modifier.height(32.dp))
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth(),
//                    .padding(top=32.dp,bottom = 32.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 🔹 DateFilter na stałe na górze
                /*item {
                    DateFilterComponent(
                        currentFilter = uiState.currentFilter,
                        isExpanded = uiState.isFilterExpanded,
                        availableFilters = uiState.availableFilters,
                        onFilterSelected = viewModel::onFilterSelected,
                        onExpandedChanged = viewModel::onFilterExpandedChanged,
                        modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                    Spacer(modifier = Modifier.height(32.dp))
                }*/

                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }


                // 🔹 Current Week Calendar
                /*item {
                    CurrentWeekCalendar(
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                }*/
                // 🔹 Current Week Calendar no padding
                item {
                    CurrentWeekCalendar(
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                }
                // 🔹 Current Week Calendar2
                item {
                    CurrentWeekCalendar2(modifier = Modifier.padding(horizontal = 16.dp))
                    Spacer(modifier = Modifier.height(24.dp))
                }
                // 🔹 Current Week Calendar3
                item {
                    CurrentWeekCalendarComponent3()
                    Spacer(modifier = Modifier.height(24.dp))
                }

                // 🔹 DonutChart
                item {
//                    Spacer(modifier = Modifier.height(24.dp))
                    DonutChart(
                        totalSpending = uiState.totalSpending,
                        categorySpendings = uiState.categorySpendings,
                        availableCategories = uiState.availableCategories,
//                    modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                }

                // 🔹 Legenda
                /*item {
                    DonutChartLegend(
                        categorySpendings = uiState.categorySpendings,
                        availableCategories = uiState.availableCategories,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    )
                }*/
                // 🔹 Legenda
                /* itemsIndexed(uiState.categorySpendings) { index, categorySpending ->
                     DonutChartLegendItem(
                         categorySpending = categorySpending,
                         availableCategories = uiState.availableCategories,
                         index = index,
                         modifier = Modifier
                             .fillMaxWidth()
                             .padding(horizontal = 16.dp)
                     )
                 }*/


                // 🔹 Divider
                item {
                    Divider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.1f),
                        thickness = 1.dp
                    )
                }

                // 🔹 HorizontalBarChart jako lista
                val sortedSpendings =
                    uiState.categorySpendings.sortedByDescending { it.categorySum }
                itemsIndexed(sortedSpendings) { index, categorySpending ->
                    HorizontalBarChartItem(
                        categorySpending = categorySpending,
                        totalSpending = uiState.totalSpending,
                        availableCategories = uiState.availableCategories,
                        index = index,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }

                // Spacer
                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }

            }
        }
    }
}

@Composable
private fun HorizontalBarChartItem(
    categorySpending: CategorySpending,
    totalSpending: Long,
    availableCategories: List<Category>,
    index: Int,
    modifier: Modifier = Modifier
) {
    val fallbackColors = listOf(
        Color(0xFF6366F1), Color(0xFF8B5CF6), Color(0xFFEC4899), Color(0xFFF59E0B),
        Color(0xFF10B981), Color(0xFF3B82F6), Color(0xFFEF4444), Color(0xFF84CC16),
        Color(0xFF06B6D4), Color(0xFFF97316)
    )

    fun getCategoryColor(categoryName: String, index: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return category?.let { Color(it.color) } ?: fallbackColors[index % fallbackColors.size]
    }

    val fraction = if (totalSpending > 0)
        categorySpending.categorySum.toFloat() / totalSpending.toFloat()
    else 0f

    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = "${categorySpending.categoryName} -- ${fraction.times(100).toInt()} %",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.weight(1f)
            )

            Text(
                text = "${categorySpending.categorySum / 100}zł",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .clip(RoundedCornerShape(6.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(fraction)
                    .fillMaxHeight()
                    .background(getCategoryColor(categorySpending.categoryName, index))
            )
        }
    }
}


@Composable
fun DonutChart(
    totalSpending: Long,
    categorySpendings: List<CategorySpending>,
    availableCategories: List<Category>,
    modifier: Modifier = Modifier,
    innerRadiusRatio: Float = 0.4f
) {
    var selectedCategoryIndex by remember { mutableIntStateOf(-1) }

    // Fallback kolory dla kategorii, które nie mają zdefiniowanego koloru
    val fallbackColors = listOf(
        Color(0xFF6366F1), // Indigo
        Color(0xFF8B5CF6), // Violet
        Color(0xFFEC4899), // Pink
        Color(0xFFF59E0B), // Amber
        Color(0xFF10B981), // Emerald
        Color(0xFF3B82F6), // Blue
        Color(0xFFEF4444), // Red
        Color(0xFF84CC16), // Lime
        Color(0xFF06B6D4), // Cyan
        Color(0xFFF97316)  // Orange
    )

    // Funkcja do znalezienia koloru dla kategorii
    fun getCategoryColor(categoryName: String, fallbackIndex: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return if (category != null) {
            Color(category.color)
        } else {
            fallbackColors[fallbackIndex % fallbackColors.size]
        }
    }

    // Obliczamy kąty dla każdej kategorii
    val angles = categorySpendings.map { categorySpending ->
        (categorySpending.categorySum.toFloat() / totalSpending.toFloat()) * 360f
    }

    // Funkcja do sprawdzenia czy punkt jest w danym łuku
    fun isPointInArc(
        offset: Offset,
        center: Offset,
        innerRadius: Float,
        outerRadius: Float,
        startAngle: Float,
        sweepAngle: Float
    ): Boolean {
        val dx = offset.x - center.x
        val dy = offset.y - center.y
        val distance = sqrt(dx * dx + dy * dy)

        if (distance < innerRadius || distance > outerRadius) return false

        var angle = (atan2(dy, dx) * 180 / PI + 360) % 360
        angle = (angle + 90) % 360 // Dostosowujemy do naszego układu współrzędnych

        val normalizedStartAngle = (startAngle + 360) % 360
        val endAngle = (normalizedStartAngle + sweepAngle) % 360

        return if (normalizedStartAngle <= endAngle) {
            angle >= normalizedStartAngle && angle <= endAngle
        } else {
            angle >= normalizedStartAngle || angle <= endAngle
        }
    }

    Box(
        modifier = modifier.size(200.dp)
            .background(color = Color.Red.copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures { offset ->
                        val canvasSize = kotlin.math.min(size.width, size.height).toFloat()
                        val radius = canvasSize / 2
                        val innerRadius = radius * innerRadiusRatio
                        val center = Offset(size.width / 2f, size.height / 2f)

                        var currentStartAngle = -90f

                        categorySpendings.forEachIndexed { index, _ ->
                            val sweepAngle = angles[index]

                            if (isPointInArc(
                                    offset = offset,
                                    center = center,
                                    innerRadius = innerRadius,
                                    outerRadius = radius,
                                    startAngle = currentStartAngle,
                                    sweepAngle = sweepAngle
                                )
                            ) {
                                selectedCategoryIndex =
                                    if (selectedCategoryIndex == index) -1 else index
                                return@detectTapGestures
                            }

                            currentStartAngle += sweepAngle
                        }

                        // Kliknięcie poza łukami - reset
                        selectedCategoryIndex = -1
                    }
                }
        ) {
            val canvasSize = size.minDimension
            val radius = canvasSize / 2
            val innerRadius = radius * innerRadiusRatio
            val strokeWidth = radius - innerRadius

            var startAngle = -90f // Zaczynamy od góry

            categorySpendings.forEachIndexed { index, categorySpending ->
                val sweepAngle = angles[index]
                val color = getCategoryColor(categorySpending.categoryName, index)

                drawArc(
                    color = color,
                    startAngle = startAngle,
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    style = Stroke(width = strokeWidth),
                    size = Size(canvasSize, canvasSize),
                    topLeft = Offset(
                        (size.width - canvasSize) / 2,
                        (size.height - canvasSize) / 2
                    )
                )

                startAngle += sweepAngle
            }
        }

        // Tekst w środku - pokazuje wybraną kategorię lub łączną kwotę
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (selectedCategoryIndex >= 0 && selectedCategoryIndex < categorySpendings.size) {
                val selectedCategory = categorySpendings[selectedCategoryIndex]
                Text(
                    text = selectedCategory.categoryName,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "${selectedCategory.categorySum / 100}zł",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Text(
                    text = "Łącznie",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "${totalSpending / 100}zł",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
fun DonutChartLegendItem(
    categorySpending: CategorySpending,
    availableCategories: List<Category>,
    index: Int,
    modifier: Modifier = Modifier
) {
    val fallbackColors = listOf(
        Color(0xFF6366F1), Color(0xFF8B5CF6), Color(0xFFEC4899), Color(0xFFF59E0B),
        Color(0xFF10B981), Color(0xFF3B82F6), Color(0xFFEF4444), Color(0xFF84CC16),
        Color(0xFF06B6D4), Color(0xFFF97316)
    )

    fun getCategoryColor(categoryName: String, fallbackIndex: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return category?.let { Color(it.color) }
            ?: fallbackColors[fallbackIndex % fallbackColors.size]
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .size(16.dp)
                .background(
                    color = getCategoryColor(categorySpending.categoryName, index),
                    shape = CircleShape
                )
        )
        Text(
            text = categorySpending.categoryName,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = "${categorySpending.categorySum / 100}zł",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}


// Komponent legendy (opcjonalny)
/*
@Composable
fun DonutChartLegend(
    categorySpendings: List<CategorySpending>,
    availableCategories: List<Category>,
    modifier: Modifier = Modifier
) {
    // Fallback kolory dla kategorii, które nie mają zdefiniowanego koloru
    val fallbackColors = listOf(
        Color(0xFF6366F1), Color(0xFF8B5CF6), Color(0xFFEC4899), Color(0xFFF59E0B),
        Color(0xFF10B981), Color(0xFF3B82F6), Color(0xFFEF4444), Color(0xFF84CC16),
        Color(0xFF06B6D4), Color(0xFFF97316)
    )

    // Funkcja do znalezienia koloru dla kategorii
    fun getCategoryColor(categoryName: String, fallbackIndex: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return if (category != null) {
            Color(category.color)
        } else {
            fallbackColors[fallbackIndex % fallbackColors.size]
        }
    }

    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        itemsIndexed(categorySpendings) { index, categorySpending ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(
                            color = getCategoryColor(categorySpending.categoryName, index),
                            shape = CircleShape
                        )
                )
                Text(
                    text = categorySpending.categoryName,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = "${categorySpending.categorySum / 100}zł",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
fun HorizontalBarChart(
    totalSpending: Long,
    categorySpendings: List<CategorySpending>,
    availableCategories: List<Category>,
    modifier: Modifier = Modifier
) {
    // Fallback kolory (te same co w DonutChart)
    val fallbackColors = listOf(
        Color(0xFF6366F1), Color(0xFF8B5CF6), Color(0xFFEC4899), Color(0xFFF59E0B),
        Color(0xFF10B981), Color(0xFF3B82F6), Color(0xFFEF4444), Color(0xFF84CC16),
        Color(0xFF06B6D4), Color(0xFFF97316)
    )

    fun getCategoryColor(categoryName: String, index: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return if (category != null) Color(category.color)
        else fallbackColors[index % fallbackColors.size]
    }

    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        itemsIndexed(categorySpendings) { index, categorySpending ->
            val fraction = if (totalSpending > 0) {
                categorySpending.categorySum.toFloat() / totalSpending.toFloat()
            } else 0f

            Column(modifier = Modifier.fillMaxWidth()) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = categorySpending.categoryName,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = "${categorySpending.categorySum / 100}zł",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(12.dp)
                        .clip(RoundedCornerShape(6.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(fraction)
                            .fillMaxHeight()
                            .background(getCategoryColor(categorySpending.categoryName, index))
                    )
                }
            }
        }
    }
}
*/
