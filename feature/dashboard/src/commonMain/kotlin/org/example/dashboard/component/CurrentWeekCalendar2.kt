package org.example.dashboard.component

import androidx.compose.runtime.Composable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.*

data class DayItem(
    val day: String,
    val date: Int,
    val month: String,
    val isToday: Boolean = false,
    val isMonthStart: Boolean = false
)

object DateUtilsCurrentWeek {

    private val dayNames = listOf("nd", "pn", "wt", "śr", "cz", "pt", "sb")
    private val monthNames = listOf(
        "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>wi<PERSON>ień", "Maj", "Czerwiec",
        "Lipiec", "Sierpień", "Wrzesień", "Październik", "Listopad", "Grudzień"
    )

    /**
     * Get current week number (ISO week)
     */
    fun getCurrentWeekNumber(): Int {
        val now = Clock.System.now()
        val localDate = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        return getWeekNumberCurrentWeek(localDate)
    }

    /**
     * Get week number for specific date (ISO week)
     */
    private fun getWeekNumberCurrentWeek(date: LocalDate): Int {
        // ISO week calculation
        val jan4 = LocalDate(date.year, 1, 4)
        val jan4DayOfWeek = jan4.dayOfWeek.ordinal // Monday = 0
        val weekOneStart = jan4.minus(jan4DayOfWeek, DateTimeUnit.DAY)

        val daysDiff = date.daysUntil(weekOneStart)
        return (-daysDiff / 7) + 1
    }

    /**
     * Get current week days starting from Monday
     */
    fun getCurrentWeekDays(): List<DayItem> {
        val now = Clock.System.now()
        val today = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        val todayDayOfWeek = today.dayOfWeek.ordinal // Monday = 0

        // Get Monday of current week
        val monday = today.minus(todayDayOfWeek, DateTimeUnit.DAY)

        return (0..6).map { dayOffset ->
            val currentDate = monday.plus(dayOffset, DateTimeUnit.DAY)
            val isMonthStart = dayOffset == 0 ||
                    currentDate.month != monday.plus(dayOffset - 1, DateTimeUnit.DAY).month

            DayItem(
                day = dayNames[currentDate.dayOfWeek.ordinal],
                date = currentDate.dayOfMonth,
                month = monthNames[currentDate.monthNumber - 1],
                isToday = currentDate == today,
                isMonthStart = isMonthStart
            )
        }
    }
}

@Composable
fun CurrentWeekCalendar2(
    modifier: Modifier = Modifier
) {
    val weekNumber by remember { mutableStateOf(DateUtilsCurrentWeek.getCurrentWeekNumber()) }
    val days by remember { mutableStateOf(DateUtilsCurrentWeek.getCurrentWeekDays()) }

    Card(
        modifier = modifier
            .shadow(
                elevation = 2.dp,
                shape = RoundedCornerShape(16.dp)
            ),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFAF8F5)
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Week number section
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(end = 16.dp)
            ) {
                Text(
                    text = "Tydzień",
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280)
                )
                Text(
                    text = weekNumber.toString(),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF111827)
                )
            }

            // Vertical divider
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .height(40.dp)
                    .background(Color(0xFFE5E5E5))
            )

            // Days section
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                days.forEachIndexed { index, day ->
                    val showMonthLabel = day.isMonthStart
                    val showDivider = index > 0 && day.month != days[index - 1].month

                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.width(44.dp)
                    ) {
                        // Month label
                        Box(
                            modifier = Modifier.height(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            if (showMonthLabel) {
                                Text(
                                    text = day.month,
                                    fontSize = 11.sp,
                                    color = Color(0xFF6B7280),
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        // Divider between months
                        if (showDivider) {
                            Box(
                                modifier = Modifier
                                    .width(1.dp)
                                    .height(32.dp)
                                    .background(Color(0xFFE5E5E5))
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                        }

                        // Day card
                        Card(
                            modifier = Modifier
                                .size(width = 44.dp, height = 56.dp),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = if (day.isToday) {
                                    Color(0xFFFFD66B)
                                } else {
                                    Color.Transparent
                                }
                            ),
                            elevation = if (day.isToday) {
                                CardDefaults.cardElevation(defaultElevation = 2.dp)
                            } else {
                                CardDefaults.cardElevation(defaultElevation = 0.dp)
                            }
                        ) {
                            Column(
                                modifier = Modifier.fillMaxSize(),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = day.day,
                                    fontSize = 11.sp,
                                    color = if (day.isToday) Color(0xFF111827) else Color(0xFF374151)
                                )
                                Spacer(modifier = Modifier.height(2.dp))
                                Text(
                                    text = day.date.toString(),
                                    fontSize = 16.sp,
                                    fontWeight = if (day.isToday) FontWeight.SemiBold else FontWeight.Medium,
                                    color = if (day.isToday) Color(0xFF111827) else Color(0xFF374151)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}