package org.example.shared.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.LocalDate
import org.example.core.utils.DateUtils
import org.jetbrains.compose.ui.tooling.preview.Preview

/**
 * Current Week Calendar component that displays the current week with day names,
 * dates, months, and highlights today. Equivalent to React component.
 */
@Composable
fun CurrentWeekCalendar(
    modifier: Modifier = Modifier,
    selectedDate: LocalDate? = null,
    onDaySelected: (LocalDate) -> Unit = {}
) {
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

//    Card(
//        modifier = modifier.fillMaxWidth(),
//        shape = RoundedCornerShape(16.dp),
//        colors = CardDefaults.cardColors(
//            containerColor = Color(0xFFFAF8F5)
//        ),
//        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
//    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Month labels row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Week number section
                Column(
                    modifier = Modifier.padding(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Tydzień",
                        fontSize = 10.sp,
                        color = Color(0xFF6B7280),
                        fontWeight = FontWeight.Normal
                    )
                    Text(
                        text = weekInfo.weekNumber.toString(),
                        fontSize = 18.sp,
                        color = Color(0xFF111827),
                        fontWeight = FontWeight.Bold
                    )
                }

                // Month labels for days
                Row(
                    modifier = Modifier.weight(1f),
//                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    weekInfo.days.forEachIndexed { index, day ->
                        Box(
//                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            if (day.isMonthStart) {
                                Text(
                                    text = day.month,
                                    fontSize = 11.sp,
//                                    color = Color(0xFF6B7280),
                                    color = Color.Red,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Days row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Empty space for week number alignment
//                Spacer(modifier = Modifier.width(60.dp))

                // Days section
                Row(
//                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    weekInfo.days.forEachIndexed { index, day ->
                        val showDivider = index > 0 &&
                            day.month != weekInfo.days[index - 1].month

                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Month divider
                            if (showDivider) {
                                Box(
                                    modifier = Modifier
                                        .width(1.dp)
                                        .height(40.dp)
                                        .background(Color(0xFFE5E5E5))
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                            }

                            // Day column
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                // Day card
                                val isSelected = selectedDate == day.localDate
                                Box(
                                    modifier = Modifier
                                        .size(width = 36.dp, height = 48.dp)
                                        .clip(RoundedCornerShape(12.dp))
                                        .background(
                                            if (day.isToday) {
                                                Color(0xFFFFD66B)
                                            } else {
                                                Color.Transparent
                                            }
                                        )
                                        .then(
                                            if (isSelected) {
                                                Modifier.border(
                                                    width = 2.dp,
                                                    color = Color(0xFF3B82F6),
                                                    shape = RoundedCornerShape(12.dp)
                                                )
                                            } else {
                                                Modifier
                                            }
                                        )
                                        .clickable {
                                            onDaySelected(day.localDate)
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = day.day,
                                            fontSize = 10.sp,
                                            color = if (day.isToday) {
                                                Color(0xFF111827)
                                            } else {
                                                Color(0xFF374151)
                                            },
                                            fontWeight = FontWeight.Normal
                                        )
                                        Spacer(modifier = Modifier.height(2.dp))
                                        Text(
                                            text = day.date.toString(),
                                            fontSize = 14.sp,
                                            color = if (day.isToday) {
                                                Color(0xFF111827)
                                            } else {
                                                Color(0xFF374151)
                                            },
                                            fontWeight = if (day.isToday) {
                                                FontWeight.SemiBold
                                            } else {
                                                FontWeight.Medium
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
//    }
}