package org.example.shared.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.core.utils.DateUtils

/**
 * Current Week Calendar component that displays the current week with day names,
 * dates, months, and highlights today. Equivalent to React component.
 */
@Composable
fun CurrentWeekCalendar(
    modifier: Modifier = Modifier
) {
    val weekInfo by remember {
        mutableStateOf(DateUtils.getCurrentWeekInfoCurrentWeek())
    }

    Card(
        modifier = modifier.wrapContentSize(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFAF8F5)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // Week number section
            Column(
                modifier = Modifier
//                    .padding(end = 16.dp)
                    .padding(end = 4.dp)
                    .wrapContentWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Tydzień",
//                    fontSize = 12.sp,
                    fontSize = 8.sp,
                    color = Color(0xFF6B7280),
                    fontWeight = FontWeight.Normal
                )
                Text(
                    text = weekInfo.weekNumber.toString(),
//                    fontSize = 24.sp,
                    fontSize = 16.sp,
                    color = Color(0xFF111827),
                    fontWeight = FontWeight.Bold
                )
            }
            Row(
                modifier = Modifier.padding(16.dp),
//                horizontalArrangement = Arrangement.spacedBy(16.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /*            // Week number section
                            Column(
                                modifier = Modifier
                //                    .padding(end = 16.dp)
                                    .padding(end = 4.dp)
                                    .wrapContentWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = "Tydzień",
                //                    fontSize = 12.sp,
                                    fontSize = 8.sp,
                                    color = Color(0xFF6B7280),
                                    fontWeight = FontWeight.Normal
                                )
                                Text(
                                    text = weekInfo.weekNumber.toString(),
                //                    fontSize = 24.sp,
                                    fontSize = 16.sp,
                                    color = Color(0xFF111827),
                                    fontWeight = FontWeight.Bold
                                )
                            }*/

                // Vertical divider
                /*Box(
                    modifier = Modifier
                        .width(1.dp)
                        .height(48.dp)
                        .background(Color(0xFFE5E5E5))
                )*/

                // Days section
                Row(
//                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.Bottom
                ) {
                    weekInfo.days.forEachIndexed { index, day ->
                        val showMonthLabel = day.isMonthStart
                        val showDivider = index > 0 &&
                                day.month != weekInfo.days[index - 1].month

                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.Bottom
                        ) {
                            // Month divider
                            if (showDivider) {
                                Box(
                                    modifier = Modifier
                                        .width(1.dp)
                                        .height(32.dp)
                                        .background(Color(0xFFE5E5E5))
                                )
                            }

                            // Day column
                            Column(
//                                modifier = Modifier.width(44.dp),
                                modifier = Modifier.wrapContentWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                // Month label
                                Box(
                                    modifier = Modifier.height(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (showMonthLabel) {
                                        Text(
                                            text = day.month,
                                            fontSize = 11.sp,
                                            color = Color(0xFF6B7280),
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }

                                Spacer(modifier = Modifier.height(4.dp))

                                // Day card
                                Box(
                                    modifier = Modifier
                                        //                                    .size(width = 44.dp, height = 56.dp)
                                        .wrapContentSize()
                                        .clip(RoundedCornerShape(12.dp))
                                        .background(
                                            if (day.isToday) {
                                                Color(0xFFFFD66B)
                                            } else {
                                                Color.Transparent
                                            }
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = day.day,
                                            //                                        fontSize = 11.sp,
                                            fontSize = 8.sp,
                                            color = if (day.isToday) {
                                                Color(0xFF111827)
                                            } else {
                                                Color(0xFF374151)
                                            },
                                            fontWeight = FontWeight.Normal
                                        )
                                        Spacer(modifier = Modifier.height(2.dp))
                                        Text(
                                            text = day.date.toString(),
                                            //                                        fontSize = 16.sp,
                                            fontSize = 12.sp,
                                            color = if (day.isToday) {
                                                Color(0xFF111827)
                                            } else {
                                                Color(0xFF374151)
                                            },
                                            fontWeight = if (day.isToday) {
                                                FontWeight.SemiBold
                                            } else {
                                                FontWeight.Medium
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
